import { useQuery } from '@tanstack/react-query'
import APIClient from '../../react-query/services/api-client'
import { FilterOptionsShape } from '../../types/types' // Assuming you have a type for the filter options

const useProductFilterOptions = (productTypeId: number) => {
  const apiClient = new APIClient<FilterOptionsShape>('/products/product-filter-options/')

  return useQuery({
    queryKey: ['filterOptions', productTypeId],
    queryFn: () => apiClient.get({
      params: { product_type_id: productTypeId }
    }),
    enabled: !!productTypeId, // Only fetch if productTypeId is truthy
    // enabled: true,
    staleTime: 1000 * 60 * 60, // 1 hours
    // cacheTime: 1000 * 60 * 60, <-- This is wrong!
    gcTime: 1000 * 60 * 60, // 1 hour
  })
}

export default useProductFilterOptions
