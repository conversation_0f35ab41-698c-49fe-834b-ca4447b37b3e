// import { useMutation, useQueryClient } from '@tanstack/react-query'
// import useOrderStore from "../../../zustand/orderStore"
// import { CACHE_KEY_ORDER_ITEMS } from "../constants"
// import APIClient from "../../services/api-client"

// const useOrderPay = () => {
//   const { orderId } = useOrderStore()
//   const queryClient = useQueryClient()

//   const apiClient = new APIClient(`/orders/${orderId}/`)

//   const updateOrder = useMutation({
//     mutationFn: () => apiClient.patch({
//       "payment_status": "Paid"
//     }),
//     onSuccess: (data) => {
//       console.log(data)
//       queryClient.invalidateQueries({
//         queryKey: [CACHE_KEY_ORDER_ITEMS]
//       })
//     },
//   })

//   return { updateOrder }

// }

// export default useOrderPay