# Tooltip Component

A reusable tooltip component that provides hover-based or conditional tooltip functionality.

## Usage

```tsx
import Tooltip from '../../utils/tooltip'

// Basic usage with hover
<Tooltip content="This is a tooltip">
  <button>Hover me</button>
</Tooltip>

// With custom position
<Tooltip content="Tooltip content" position="bottom">
  <span>Element with tooltip</span>
</Tooltip>

// Conditional tooltip (always visible when showOnCondition is true)
<Tooltip 
  content="Please select all options" 
  showOnCondition={hasErrors}
  showOnHover={false}
>
  <button disabled={hasErrors}>Add to Cart</button>
</Tooltip>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `children` | `ReactNode` | - | The element that triggers the tooltip |
| `content` | `string` | - | The text content of the tooltip |
| `position` | `'top' \| 'bottom' \| 'left' \| 'right'` | `'top'` | Position of the tooltip relative to the trigger element |
| `disabled` | `boolean` | `false` | Whether to disable the tooltip completely |
| `className` | `string` | `''` | Additional CSS classes to apply |
| `showOnHover` | `boolean` | `true` | Whether to show tooltip on hover |
| `showOnCondition` | `boolean` | `false` | Whether to always show tooltip (useful for validation messages) |

## Features

- Multiple positioning options (top, bottom, left, right)
- Hover-based tooltips (default behavior)
- Conditional tooltips (always visible when needed)
- Responsive design with mobile optimizations
- Smooth fade-in/fade-out animations
- Arrow indicators for better visual connection
- High z-index to ensure visibility above other elements

## Styling

The component uses CSS modules with the following classes:
- `.tooltip` - Base tooltip container
- `.tooltip--{position}` - Position-specific styles
- `.tooltip--hover` - Hover-triggered tooltips
- `.tooltip--condition` - Conditionally visible tooltips

The tooltip appearance can be customized by modifying `Tooltip.module.scss`.
