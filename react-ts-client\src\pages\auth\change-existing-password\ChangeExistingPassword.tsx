import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { changeExistingPasswordSchema } from '../../../types/schemas'
import Underlay from '../../../components/utils/underlay/Underlay'
import Logo from '../../../components/utils/logo/Logo'
import Alert from '../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../types/types'
import useSendSetPassword from '../../../react-query/hooks/auth-hooks/password-changes/change-existing-password/useSendSetPassword'
import useLogout from '../../../react-query/hooks/auth-hooks/useLogOut'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import useTogglePasswordVisibility from '../../../hooks/useTogglePasswordVisibility'
import loading_blue from '../../../assets/loading_svg_blue.svg'
import authStore from '../../../zustand/authStore'
import styles from './ChangeExistingPassword.module.scss'

export type ChangePasswordShape = z.infer<typeof changeExistingPasswordSchema>

const ChangeExistingPassword = () => {
  const navigate = useNavigate()
  const { isLoggedIn } = authStore()
  const { mutation } = useSendSetPassword()
  const { mutation: userLogout } = useLogout()

  const { isVisible: isCurrentPasswordVisible, toggleVisibility: toggleCurrentPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isNewPasswordVisible, toggleVisibility: toggleNewPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isReNewPasswordVisible, toggleVisibility: toggleReNewPasswordVisibility } = useTogglePasswordVisibility()

  const { register, handleSubmit, formState: { errors } } = useForm<ChangePasswordShape>({
    resolver: zodResolver(changeExistingPasswordSchema)
  })

  const onSubmit: SubmitHandler<ChangePasswordShape> = async (data) => {
    const { old_password, new_password, confirm_password } = data
    mutation.mutate({
      old_password,
      new_password,
      confirm_password,
    }, {
      onSuccess: () => {
        userLogout.mutate()
      },
    })
  }

  useEffect(() => {
    if (!isLoggedIn) {
      navigate('/user/login/')
    }
  }, [isLoggedIn, navigate])

  return (
    <Underlay isOpen>
      {isLoggedIn && <div className={styles.new_password}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
        {mutation.isSuccess ?
          <>
            <p className='success_message'>Your password resetting is complete. You can now Login with your new password.</p>
            <section className='btn_container'>
              <button className='empty_btn' onClick={() => navigate('/user/login/')}>Login</button>
            </section>
          </> :
          <form onSubmit={handleSubmit(onSubmit)} className='form'>
            <div className='form_group'>
              <label className='form_label' htmlFor="old_password">Current Password:</label>
              <section className='password__container'>
                <input
                  className='form_input'
                  type={isCurrentPasswordVisible ? "text" : "password"}
                  id="old_password"
                  {...register('old_password')} />
                <span onClick={toggleCurrentPasswordVisibility}>
                  <i>{isCurrentPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.old_password && <span>{errors.old_password.message}</span>}
            </div>
            <div className='form_group'>
              <label className='form_label' htmlFor="new_password">New Password:</label>
              <section className='password__container'>
                <input
                  className='form_input'
                  disabled={mutation.isPending}
                  type={isNewPasswordVisible ? "text" : "password"}
                  id="new_password"
                  {...register('new_password')}
                />
                <span onClick={toggleNewPasswordVisibility}>
                  <i>{isNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.new_password && <p>{errors.new_password.message} &#128543;</p>}
            </div>
            <div className='form_group'>
              <label className='form_label' htmlFor="confirm_password">Confirm New Password:</label>
              <section className='password__container'>
                <input
                  className='form_input'
                  disabled={mutation.isPending}
                  type={isReNewPasswordVisible ? "text" : "password"}
                  id="confirm_password"
                  {...register('confirm_password')}
                />
                <span onClick={toggleReNewPasswordVisibility}>
                  <i>{isReNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
                </span>
              </section>
              {errors.confirm_password && <p>{errors.confirm_password.message} &#128543;</p>}
            </div>
            <section className='btn_container'>
              <button
                className='empty_btn'
                disabled={mutation.isPending}
                type="button"
                onClick={() => navigate('/customer')}>Cancel
              </button>
              <button type="submit" className='empty_btn' disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <img src={loading_blue} alt="Loading..." className='loading_svg' />
                ) : (
                  'Reset Password'
                )}
              </button>
            </section>
          </form>
        }
      </div>}
    </Underlay>
  )
}

export default ChangeExistingPassword
