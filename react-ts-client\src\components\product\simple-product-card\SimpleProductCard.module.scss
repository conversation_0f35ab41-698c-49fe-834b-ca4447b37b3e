@import '../../../scss/variables';
@import '../../../scss/mixins';

.product_card {
  width: 100%;
  max-width: 250px;
  border-radius: $border-radius-1;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background-color: #ffffff;
  box-shadow: $box-shadow-1;
  padding: 2px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-2;
  }

  a {

    .product_card__image {
      position: relative;
      padding-top: 100%;
      overflow: hidden;

      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;

        &:hover {
          transform: scale(1.03);
        }
      }
    }

    .product_card__info {
      padding: .5rem;

      h3 {
        color: $primary-dark-text-color;
        height: 3rem; // Limit to 2 lines
        transition: color .3s ease;

        &:hover {
          color: $primary-blue;
        }
      }

      .rating {
        background-color: aquamarine;
        margin: 5px 0;
        @include flexbox(flex-start, center);

        span:nth-child(2) {
          padding: 0 4px;
          // background-color: $primary-blue;
        }
      }

      .price {
        span:first-child {
          font-size: $font-size-3;
          font-weight: bold;
          color: $primary-blue;
        }

        span:last-child {
          color: $primary-dark-text-color;
          text-decoration: line-through;
          font-size: 15px;
          margin: 0 0 0 4px;
        }
      }
    }
  }
}