import { useQuery } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { ProductShape } from '../../../types/product-types'


const useProductDetails = (slug: string) => {
  const apiClient = new APIClient<ProductShape>(`/products/${slug}`)

  return useQuery({
    queryKey: ['product', slug],
    queryFn: () => apiClient.get(),
    // enabled: !!slug, // Only fetch data if slug is provided
  })
}

export default useProductDetails
