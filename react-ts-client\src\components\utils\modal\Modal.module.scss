@import '../../../scss/variables';
@import '../../../scss/mixins';

.modal {
  margin: 2rem 1rem 0 1rem;
  background-color: #fff;
  border-radius: $border-radius-2;
  // width: 100%;
  // max-width: 800px;
  padding: $padding-5;
  position: relative;
  align-self: flex-start;

  .modal_close {
    @include flexbox(center, center);
    position: absolute;
    background-color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    top: 0px !important;
    right: -35px;
    font-size: 24px;
    margin: 0 0 0 0;
    transition: all 0.2s ease;

    &:hover {
      background-color: $lighten-blue;
    }

    i {
      background-color: none;
      color: black;
    }
  }
}

.modal_body {
  text-align: center;

  &__title {
    font-size: $font-size-4;
    font-weight: bold;
  }

  &__message {
    margin: 10px 0;
    font-size: $font-size-3;
  }

  .modal_actions {
    @include flexbox(space-around, center);
    margin-top: 20px;
    column-gap: 10px;
    // background-color: #bda2a2;

    .btn_confirm {
      @include btn(#fff, $primary-blue);
      // width: fit-content;
      padding: .36rem 1.2rem;
      letter-spacing: .7px;
      transition: all 0.3s ease;

      &:hover {
        background-color: darken($lighten-blue, 15%);
      }
    }

    .empty_btn {
      @include btn($lighten-blue, #fff);
      // margin: 0 auto 0 auto;
      padding: .36rem 1.2rem;
      border: 1px solid $lighten-blue;
      letter-spacing: .7px;
      transition: all 0.3s ease;

      &:hover {
        border: 1px solid $primary-blue;
        color: $primary-blue;
      }
    }
  }
}