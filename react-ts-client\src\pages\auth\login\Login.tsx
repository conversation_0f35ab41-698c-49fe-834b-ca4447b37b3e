import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { <PERSON> } from "react-router-dom"
import Logo from '../../../components/utils/logo/Logo'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { loginSchema } from '../../../types/schemas'
import useLogin from '../../../react-query/hooks/auth-hooks/useLogin'
import Alert from '../../../components/utils/alert/Alert'
import loading from '../../../assets/loading_svg_white.svg'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../types/types'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import { getErrorMessage } from '../../../components/utils/getErrorMessage'
import useTogglePasswordVisibility from '../../../hooks/useTogglePasswordVisibility'
import styles from './Login.module.scss'


type LoginShape = z.infer<typeof loginSchema>

const Login = () => {
  const { isVisible, toggleVisibility } = useTogglePasswordVisibility()
  const { mutation } = useLogin()

  const { register, handleSubmit, formState: { errors } } = useForm<LoginShape>({
    resolver: zodResolver(loginSchema)
  })

  const onSubmit: SubmitHandler<LoginShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        // reset()
        // setTimeout(() => {
        //   navigate('/')
        //   // navigate('/checkout/')
        // }, 4000)
      }
    })
  }


  return (
    <div className={styles.login_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.isSuccess ?
          <div>
            <Alert variant="success" message='Login successful.' textAlign='center' />
            <div className={styles.login_nav}>
              <h3>Navigate me to:</h3>
              <div>
                <Link to='/checkout'>Continue Checkout</Link>
                <Link to='/customer'>Update Profile</Link>
                <Link to='/cart'>Shopping Cart</Link>
              </div>
            </div>
          </div> :
          <div>
            {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
            {/* {mutation.error && <Alert variant="error" message={`Login failed. Please try again later.`} />} */}
            <h2 className='title'>Login</h2>
            <form onSubmit={handleSubmit(onSubmit)} className={styles.form}>
              <div>
                <label htmlFor="username">Email or phone number:</label>
                <input placeholder={` Eg: +94789999999`} type="text" id="username" {...register("username")} />
                {errors.username && <p>{errors.username.message}</p>}
              </div>
              <div>
                <div className={styles.password__reset}>
                  <label htmlFor="password">Password:</label>
                  <Link to='/user/password-reset/'>Forget password?</Link>
                </div>
                <section className='password__container'>
                  <input
                    type={isVisible ? "text" : "password"} id="password" {...register("password")} />
                  <span onClick={toggleVisibility}>
                    <i>{isVisible ? <FaEyeSlash /> : <FaEye />}</i>
                  </span>
                </section>
                {errors.password && <p>{errors.password.message}</p>}
              </div>
              <button className={styles.login_btn} type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <img src={loading} alt="Loading..." className='loading_svg' />
                ) : (
                  'Login'
                )}
              </button>
              <p className={styles.login_or_register}>Don't have an account yet? <Link to='/user/register'>Register</Link></p>
            </form>
          </div>
        }
      </div>
    </div>
  )
}

export default Login
