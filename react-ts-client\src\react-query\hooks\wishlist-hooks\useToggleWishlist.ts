import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { WISHLIST_ITEMS } from "../constants"

const useToggleWishlist = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/wishlist/toggle_product/`)

  const mutation = useMutation({
    mutationFn: (productId: number) => apiClient.post({
      "product_id": `${productId}`
    }),
    // onMutate: async (itemId) => {
    //   await queryClient.cancelQueries(CACHE_KEY_CART_ITEMS)

    //   const previousCartItems = queryClient.getQueryData(CACHE_KEY_CART_ITEMS)

    //   queryClient.setQueryData(CACHE_KEY_CART_ITEMS, (oldData: CartShape) => {
    //     const updatedCartItems = oldData?.cart_items.filter(
    //       (item) => item.id !== itemId
    //     )
    //     return { ...oldData, cart_items: updatedCartItems }
    //   })

    //   return { previousCartItems }
    // },

    onSuccess: () => {
      // queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
      queryClient.invalidateQueries({
        queryKey: [WISHLIST_ITEMS]
      })
    },

    // onError: (err, itemId, context) => {
    //   if (context?.previousCartItems) {
    //     queryClient.setQueryData(CACHE_KEY_CART_ITEMS, context.previousCartItems)
    //   }
    // },

    // onSettled: async () => {
    //   return await queryClient.invalidateQueries({
    //     queryKey: CACHE_KEY_CART_ITEMS
    //   })
    // },

    // onSettled: () => {
    //   queryClient.invalidateQueries(CACHE_KEY_CART_ITEMS)
    //   // queryClient.invalidateQueries({ queryKey: CACHE_KEY_CART_ITEMS });
    // },

  })

  // const handleDeleteCartItem = (itemId) => {
  //   mutate({ itemId })
  // }

  // return { isPending, isError, mutate }

  return mutation

}

export default useToggleWishlist