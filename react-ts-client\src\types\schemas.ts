import { z } from 'zod'
import { subYears, isBefore } from 'date-fns' // using date-fns for date manipulation

// Common Schema Logics
export const phoneNumberSchema = z.string().regex(/^\+?[1-9]\d{1,14}$/, {
  message: "Invalid phone number",
})

export const emailSchema = z.string().email({
  message: "Invalid email address",
})


export const loginSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Enter a valid email or phone number." }) // Ensure the field is not empty
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number", // Custom error message if neither is valid
      }
    ),
  // .refine((e) => e === "<EMAIL>", "This email is not in our database"),
  password: z.string().min(1, 'Password is required'),
})

export const resetPasswordInitSchema = z.object({
  email_or_phone: z
    .string()
    .min(1, { message: "Enter a valid email or phone number." }) // Ensure the field is not empty
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number", // Custom error message if neither is valid
      }
    ),
})


export const restPasswordVerifySchema = z.object({
  email_or_phone: z
    .string()
    .min(1, { message: "Enter a valid email or phone number." }) // Ensure the field is not empty
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number", // Custom error message if neither is valid
      }
    ),
  code: z.number().int()
    .min(100000, { message: "Verification code must be 6 digits" }) // Minimum 6 digits
    .max(999999, { message: "Verification code must be 6 digits" }), // Maximum 6 digits
  new_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one digit' })
    .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' }),
  confirm_password: z.string()
    .min(8, { message: 'Confirm password must be at least 8 characters long' }),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ['confirm_password'],
})

// export const registerSchema = z.object({
//   first_name: z.string().min(1, { message: 'First name is required' }),
//   last_name: z.string().min(1, { message: 'Last name is required' }),
//   email: z.string()
//     .min(1, { message: "Email is required" })
//     .email({ message: "Invalid email address" }),
//   password: z.string()
//     .min(8, { message: 'Password must be at least 8 characters long' })
//     .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
//     .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
//     .regex(/[0-9]/, { message: 'Password must contain at least one digit' })
//     .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' })
//     .regex(/^\S*$/, { message: 'Password must not contain spaces' })
//     .min(1, { message: 'Password is required' }),
//   re_password: z.string().min(1, { message: 'Confirm password is required' }),
// }).refine((data) => data.password === data.re_password, {
//   message: "Passwords don't match",
//   path: ['re_password'],
// })

export const updateCustomerSchema = z.object({
  first_name: z.string().min(1, { message: 'First name is required' }),
  last_name: z.string().min(1, { message: 'Last name is required' }),
  birth_date: z
    .string()
    .min(1, { message: 'Birth date is required' })
    .regex(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in the format YYYY-MM-DD' }) // Ensures correct date format
    .refine((val) => {
      const birthDate = new Date(val)
      const today = new Date()
      const minAgeDate = subYears(today, 18) // Date for 18 years ago
      return isBefore(birthDate, minAgeDate) // Ensure birth date is before the cutoff
    }, { message: 'Customer must be at least 18 years old' })
})




// export const verificationCodeSchema = z.string()
//   .regex(/^\d{6}$/, {
//     message: "Verification code must be exactly 6 digits, numeric only",
//   })
//   .transform((val) => Number(val))
//   .refine((val) => !isNaN(val) && val >= 0 && val <= 999999, {
//     message: "Verification code must be a number between 000000 and 999999",
//   })

export const registerSchema = z.object({
  username: z
    .string()
    .min(1, { message: "Email or phone number is required" }) // Ensure the field is not empty
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number", // Custom error message if neither is valid
      }
    ),
})


// export const verificationCodeSchema = z.object({
//   code: z.number()
//     .int()
//     .min(100000, { message: "Verification code must be 6 digits" }) // Minimum 6 digits
//     .max(999999, { message: "Verification code must be 6 digits" }) // Maximum 6 digits
//   // code: z.string(),
//   // phone_number: z.string().optional(),
//   // email: z.string().optional()
// })
// // .refine((data) => data.phone_number || data.email, {
// //   message: "Either phone number or email must be provided."
// // })

// export const verificationCodeSchema = z.object({
//   code: z.string()
//     .transform((value) => value.replace(/\s+/g, ''))
//     .pipe(z.coerce.number().int())
//     .refine(
//       (value) => value >= 100000 && value <= 999999,
//       { message: "Verification code must be 6 digits" }
//     )
// })


export const verificationCodeSchema = z.object({
  code: z.string()
    .min(1, { message: "Verification code is required" }) // Catch empty inputs
    .transform((value) => value.trim()) // Trim leading/trailing whitespace
    .pipe(
      z.string()
        .regex(/^\d{6}$/, { message: "Verification code must be exactly 6 digits" }) // Ensure 6 digits
        .transform((value) => Number(value)) // Convert to number after validation
    )
})

export const updateAuthInfoSchema = z.object({
  altUsername: z.string()
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    ),
  code: z.number().int()
    .min(100000, { message: "Verification code must be 6 digits" }) // Minimum 6 digits
    .max(999999, { message: "Verification code must be 6 digits" }) // Maximum 6 digits
})

export const contactInfoSchema = z.object({
  altUsername: z.string()
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .refine(
      (value) => emailSchema.safeParse(value).success || phoneNumberSchema.safeParse(value).success,
      {
        message: "Invalid email or phone number",
      }
    )
})

export const verifyContactSchema = z.object({
  altUsername: z.string()
    .transform((value) => value.replace(/\s+/g, '')), // Remove all spaces,
  code: z.number().int()
    .min(100000, { message: "Verification code must be 6 digits" })
    .max(999999, { message: "Verification code must be 6 digits" })
})

export const changeAuthInfoSchema = z.object({
  email: z.string()
    .email({ message: "Invalid email address" })
    .min(1, { message: "Email is required" })
    .transform((value) => value.replace(/\s+/g, '')) // Remove all spaces
    .optional(),
  phone_number: phoneNumberSchema.optional()
})


export const addressSchema = z.object({
  id: z.number().optional(),
  full_name: z.string().min(1, { message: 'Full name is required' }),
  street_name: z.string().min(1, { message: 'Street name is required' }),
  address_line_1: z.string().max(100, { message: 'Address line 1 should not exceed 30 characters' }).optional(),
  address_line_2: z.string().max(100, { message: 'Address line 1 should not exceed 30 characters' }).optional(),
  postal_code: z.string().min(1, { message: 'Postal code is required' }),
  city_or_village: z.string().min(1, { message: 'City or village is required' }),
})

export const profileSchema = z.object({
  id: z.number(),
  email: z.string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Invalid email address' }),
  first_name: z.string()
    .min(1, { message: 'First name is required' })
    .max(30, { message: 'First name should not exceed 30 characters' })
    .regex(/^[A-Za-z]+$/, { message: 'First name should only contain letters' }),
  last_name: z.string()
    .min(1, { message: 'Last name is required' })
    .max(30, { message: 'Last name should not exceed 30 characters' })
    .regex(/^[A-Za-z]+$/, { message: 'Last name should only contain letters' }),
  phone_number: z.string().min(1, { message: 'Phone number is required' }),
  // address: z.optional(z.array(addressSchema)),
  // birth_date: z.string().optional(),
})

export const resetPasswordSchema = z.object({
  new_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one digit' })
    .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' }),
  re_new_password: z.string()
    .min(8, { message: 'Confirm password must be at least 8 characters long' }),
}).refine((data) => data.new_password === data.re_new_password, {
  message: "Passwords don't match",
  path: ['re_new_password'],
})

export const changeExistingPasswordSchema = z.object({
  old_password: z.string().min(1, { message: 'Current password is required' }),
  new_password: z.string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one digit' })
    .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' }),
  confirm_password: z.string()
    .min(8, { message: 'Confirm password must be at least 8 characters long' }),
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ['confirm_password'],
})

export const setPasswordSchema = z.object({
  password: z.string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[A-Z]/, { message: 'Password must contain at least one uppercase letter' })
    .regex(/[a-z]/, { message: 'Password must contain at least one lowercase letter' })
    .regex(/[0-9]/, { message: 'Password must contain at least one digit' })
    .regex(/[^A-Za-z0-9]/, { message: 'Password must contain at least one special character' }),
  confirm_password: z.string()
    .min(8, { message: 'Confirm password must be at least 8 characters long' }),
}).refine((data) => data.password === data.confirm_password, {
  message: "Passwords don't match",
  path: ['confirm_password'],  // used to specify which field in the schema the validation error should be associated with
})

export const upDateProfileSchema = z.object({
  first_name: z.string()
    .min(1, { message: 'First name is required' })
    .max(30, { message: 'First name should not exceed 30 characters' })
    .regex(/^[A-Za-z]+$/, { message: 'First name should only contain letters' }),
  last_name: z.string()
    .min(1, { message: 'Last name is required' })
    .max(30, { message: 'Last name should not exceed 30 characters' })
    .regex(/^[A-Za-z]+$/, { message: 'Last name should only contain letters' }),
})


// export const emailSchema = z.object({
//   email: z.string()
//     .min(1, { message: "Email is required" })
//     .email({ message: "Invalid email address" })
// })

export const newEmailSchema = z.object({
  email: z.string()
    .min(1, { message: "Email is required" })
    .email({ message: "Invalid email address" })
})


export const stripeCheckoutSchema = z.object({
  amount: z.number().positive(),
  // orderId: z.number().min(1),
  // customerId: z.number().min(1),
  // fullName: z.string().min(1),
})
