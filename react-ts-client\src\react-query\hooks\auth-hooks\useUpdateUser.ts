// import { useMutation, useQueryClient } from '@tanstack/react-query'
// import APIClient from "../../services/auth-client"
// import { UpdateProfileFormInputs } from '../../../pages/auth/profile/Profile'
// import { CUSTOMER_DETAILS } from '../constants'


// const useUpdateUser = () => {
//   const queryClient = useQueryClient()

//   const apiClient = new APIClient<UpdateProfileFormInputs>(`/users/me/`)

//   const mutation = useMutation({
//     mutationFn: (userDetails: UpdateProfileFormInputs) => apiClient.patch(userDetails),
//     onSuccess: () => {
//       queryClient.invalidateQueries({
//         queryKey: [CUSTOMER_DETAILS]
//       })
//     }
//   })

//   return { mutation }
// }

// export default useUpdateUser