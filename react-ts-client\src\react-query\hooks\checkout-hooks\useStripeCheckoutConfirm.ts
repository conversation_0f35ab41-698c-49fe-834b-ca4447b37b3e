// import { useMutation } from '@tanstack/react-query'
// import APIClient from "../../services/api-client"

// interface ConfirmParams {
//   orderId: number
//   paymentIntentId: string // Optional in case it's not always needed
// }

// const useStripeCheckoutConfirm = ({ orderId, payment_intent_id }: ConfirmParams) => {

//   const apiClient = new APIClient(`/payments/payment-intent-confirm/`)

//   const stripePayConfirm = useMutation({
//     mutationFn: () => apiClient.post({
//       params: { order_id: orderId, },
//       data: { payment_intent_id: paymentIntentId }
//     }),
//   })

//   return { stripePayConfirm }
// }

// export default useStripeCheckoutConfirm
