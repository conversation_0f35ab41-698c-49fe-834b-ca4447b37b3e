import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import { verificationCodeSchema } from '../../../../types/schemas'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { ErrorResponse } from '../../../../types/types'
import loading_blue from '../../../../assets/loading_svg_blue.svg'
import Underlay from '../../../../components/utils/underlay/Underlay'
import Logo from '../../../../components/utils/logo/Logo'
import Alert from '../../../../components/utils/alert/Alert'
import authStore from '../../../../zustand/authStore'
import useSendVerifyRegCredentials from '../../../../react-query/hooks/auth-hooks/registration/useSendVerifyRegCredentials'
import styles from './VerifyRegCredentials.module.scss'

type VerifyCodeFormInputs = z.infer<typeof verificationCodeSchema>

export interface VerificationCredentials {
  code: number
  email?: string
  phone_number?: string
}

const VerifyRegCredentials = () => {
  const navigate = useNavigate()
  const { username,
    regInitiated,
    setRegInitiated,
    setUsername,
    verificationCodeSubmitted,
    setVerificationCodeSubmitted,
  } = authStore()
  const { mutation } = useSendVerifyRegCredentials()


  const { register, handleSubmit, formState: { errors }, watch } = useForm<VerifyCodeFormInputs>({
    resolver: zodResolver(verificationCodeSchema)
  })

  const allValues = watch()

  console.log(typeof (allValues.code))

  console.log('Username in VerifyRegCredentials:', username)

  useEffect(() => {
    if (verificationCodeSubmitted) {
      navigate('/user/set-password')
    }
  }, [verificationCodeSubmitted, navigate])


  useEffect(() => {
    if (!regInitiated) {
      navigate('/user/register')
    }

    if (mutation.isSuccess) {
      navigate('/user/set-password')
    }

    // If a user did not continue the registration process, redirect them to the registration page after 15 minutes
    // setTimeout(() => {
    //   setUsername(null)
    //   setRegInitiated(false)
    //   navigate('/user/register')
    // }, 900000) // 15 minutes
  }, [setRegInitiated, mutation.isSuccess, navigate, setUsername, regInitiated])


  const onSubmit: SubmitHandler<VerifyCodeFormInputs> = (data) => {

    const submissionData: VerificationCredentials = { code: data.code }

    if (username) {
      if (username.startsWith('+')) {
        submissionData.phone_number = username
      } else {
        submissionData.email = username
      }
    }
    mutation.mutate(submissionData, {
      onSuccess: () => {
        setVerificationCodeSubmitted(true)
      }
    })  // Submit the mutation
  }


  return (
    <Underlay isOpen>
      <div className={styles.register_container}>
        {regInitiated &&
          <div className={styles.reset_password}>
            <div className='logo_header'>
              <Logo />
            </div>
            {mutation.error && <>
              <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />

              {/* <button onClick={() => navigate('/user/register/')} className='empty_btn'>Request a new code</button> */}
            </>
            }
            <>
              <Alert
                variant="success"
                message={`A Verification Code has been sent to ${username}. 
                Please check your ${username?.startsWith('+') ? 'phone' : 'email'} and enter the code below.`}
                textAlign='center'
                highlightWords={[`${username}`, "phone", "email"]}
              />
              <form onSubmit={handleSubmit(onSubmit)} className='form' noValidate={true}>
                <div className='form_group'>
                  <label className='form_label' htmlFor="verification_code">Enter verification code:</label>
                  <section className='password__container'>
                    {/* <input
                      className='form_input'
                      type='text'
                      inputMode='numeric'
                      id='verification_code'
                      disabled={mutation.isPending}
                      {...register("code")}
                    /> */}
                    <input
                      className='form_input'
                      type='text'
                      inputMode='numeric'
                      id='verification_code'
                      disabled={mutation.isPending}
                      onInput={(e) => {
                        e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
                      }}
                      {...register("code")}
                    // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
                    />
                  </section>
                  {errors.code && <p className='form_error'>{errors.code.message} &#128543;</p>}
                </div>
                <section className='btn_container'>
                  <button type="submit" className='empty_btn' disabled={mutation.isPending}>
                    {mutation.isPending ? (
                      <img src={loading_blue} alt="Loading..." className='loading_svg' />
                    ) : (
                      'Submit'
                    )}
                  </button>
                </section>
              </form>
            </>
          </div>
        }
      </div>
    </Underlay>
  )
}

export default VerifyRegCredentials
