@use './variables' as vars;

// Generic flexbox mixin
@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $direction;
  flex-wrap: $wrap;
}


@mixin btn($color, $bg-color) {
  @include flexbox(center, center);
  color: $color;
  background-color: $bg-color;
  padding: 5px 10px;
  border-radius: 3px;
}

// @mixin theme($light-theme: true) {
//   @if $light-theme {
//     background-color: lighten($primary-dark, 100%);
//     color: darken($text-color, 100%);
//   }
// }

// add this class 
// .light {
//   @include theme(true);
//   // @include theme($light-theme: true);
// }


@mixin mobile {
  @media (max-width: vars.$mobile) {
    @content;
  }
}

@include mobile {
  // define what do you wanna do below 430px (mobile)
  // ex: flex-direction: column;
}

// Dynamic Mixin for List Styling
@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {
  @for $i from 0 through $max-levels {
    .level-#{$i} {
      margin-left: $i * $gap;
      list-style: circle;
      color: darken($base-color, $i * 10%);

      // Example: Different list styles for different levels
      @if $i % 3==0 {
        list-style: disc;
      }

      @else if $i % 3==1 {
        list-style: square;
      }

      @else {
        list-style: none;
      }

      // Adjust for flex styles
      @if $i ==0 {
        @include flexbox(flex-start, flex-start, row);
        flex-wrap: wrap;
      }

      @else {
        display: block;
      }
    }
  }
}



// Extensions in scss