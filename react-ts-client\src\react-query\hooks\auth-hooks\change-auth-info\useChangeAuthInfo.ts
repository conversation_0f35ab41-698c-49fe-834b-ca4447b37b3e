import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../services/auth-client"
import { NewAuthInfoShape } from '../../../../pages/auth/profile/change-auth-info/ChangePhoneNumber'


// type LoginUserShape = z.infer<typeof loginSchema>

const useChangeAuthInfo = () => {
  const apiClient = new APIClient(`add-contact/initiate/`)

  const mutation = useMutation({
    mutationFn: (data: NewAuthInfoShape) => apiClient.patch(data),
    onSuccess: () => { // data is the response data
      // const { access } = data.data
      // login()
    }
    // onSettled: (data, error, variables) => {
    //   console.log(data)
    //   console.log(error)
    //   console.log(variables) // variables are user input data
    // }
  })

  return { mutation }
}

export default useChangeAuthInfo