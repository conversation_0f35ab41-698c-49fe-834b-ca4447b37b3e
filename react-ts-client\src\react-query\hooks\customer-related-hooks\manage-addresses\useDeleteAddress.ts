import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"
import { CUSTOMER_DETAILS } from '../../constants'

const useDeleteAddress = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient('/customers/addresses')

  const deleteAddress = useMutation({
    mutationFn: (addressId: number) => apiClient.delete(addressId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { deleteAddress }
}

export default useDeleteAddress
