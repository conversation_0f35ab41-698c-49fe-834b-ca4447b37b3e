import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../../services/auth-client"
import { AxiosError } from 'axios'
import { ResetRequestShape } from '../../../../../pages/auth/resetting-forgotten-password/PasswordResetRequest'
import { ErrorResponse } from '../../../../../types/types'


type ResetPasswordResponseShape = {
  message: string
  email_or_phone: string
}

const useInitResetPassword = () => {

  const apiClient = new APIClient<ResetPasswordResponseShape, ResetRequestShape>(`/password-reset-request/`)

  const mutation = useMutation<ResetPasswordResponseShape, AxiosError<ErrorResponse>, ResetRequestShape>({
    mutationFn: (newPassword: ResetRequestShape) => apiClient.post(newPassword),
  })

  return { mutation }
}

export default useInitResetPassword