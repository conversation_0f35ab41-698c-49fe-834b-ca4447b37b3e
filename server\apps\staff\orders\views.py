from django.db.models import Prefetch, <PERSON>, Count
from django.utils import timezone
from datetime import datetime
from rest_framework.viewsets import ModelViewSet, ReadOnlyModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from apps.order.models import Order, OrderItem
from apps.staff.authorization.permissions import IsStaffUser
from apps.staff.common.pagination import StaffPagination
from .permissions import (
    CanManageOrders, CanChangeOrderStatus, CanAssignOrders,
    CanViewOrderReports, CanAddOrderNotes
)
from .models import OrderProxy, OrderStatusHistory, OrderAssignment, OrderNote, BulkOrderOperation, OrderDocument
from .serializers import (
    StaffOrderSerializer, OrderStatusUpdateSerializer, OrderSummarySerializer,
    CreateOrderAssignmentSerializer, CreateOrderNoteSerializer, OrderAssignmentSerializer,
    OrderNoteSerializer, OrderStatusHistorySerializer, BulkOrderStatusUpdateSerializer,
    BulkOrderAssignmentSerializer, BulkDocumentGenerationSerializer, BulkOrderOperationSerializer,
    OrderDocumentSerializer
)
from apps.staff.common.constants import STAFF_GROUPS


class StaffOrderViewSet(ModelViewSet):
    """
    Staff-specific order management ViewSet.
    Provides comprehensive order management functionality for staff users.
    """
    permission_classes = [IsStaffUser, CanManageOrders]
    serializer_class = StaffOrderSerializer
    pagination_class = StaffPagination

    def get_queryset(self):
        """Filter orders based on staff role and permissions"""
        user = self.request.user
        base_queryset = Order.objects.select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related(
            Prefetch(
                'ordered_items',
                queryset=OrderItem.objects.select_related('product', 'product_variant')
            ),
            'status_history__changed_by',
            'assignments__assigned_to',
            'staff_notes__created_by'
        )

        if user.is_superuser:
            return base_queryset

        # Filter based on user's role and permissions
        user_groups = set(user.groups.values_list('name', flat=True))

        # Order Management Executive - all orders
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return base_queryset

        # Order Fulfillment Specialist - pending and processing orders
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            return base_queryset.filter(
                delivery_status__in=['Pending', 'Processing']
            )

        # Order Management Group Member - limited access
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            return base_queryset.filter(
                delivery_status__in=['Pending', 'Processing', 'Dispatched']
            )

        # Customer Service - can view orders for support
        if STAFF_GROUPS['CUSTOMER_SERVICE'] in user_groups:
            return base_queryset

        return Order.objects.none()

    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if self.action == 'list':
            return OrderSummarySerializer
        return StaffOrderSerializer

    @action(detail=True, methods=['patch'], permission_classes=[CanChangeOrderStatus])
    def update_status(self, request, pk=None):
        """Update order delivery status with audit trail"""
        order = self.get_object()
        serializer = OrderStatusUpdateSerializer(
            data=request.data,
            context={'order': order}
        )

        if serializer.is_valid():
            old_status = order.delivery_status
            new_status = serializer.validated_data['delivery_status']
            notes = serializer.validated_data.get('notes', '')

            # Update order
            order.delivery_status = new_status
            order.save()

            # Create audit trail
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=old_status,
                new_status=new_status,
                changed_by=request.user.staff_profile,
                notes=notes
            )

            return Response({
                'status': 'success',
                'message': 'Order status updated successfully',
                'previous_status': old_status,
                'new_status': new_status
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[CanAssignOrders])
    def assign(self, request, pk=None):
        """Assign order to a staff member"""
        order = self.get_object()
        serializer = CreateOrderAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            # Deactivate existing assignments
            order.assignments.filter(is_active=True).update(is_active=False)

            # Create new assignment
            from apps.staff.authorization.models import StaffProfile
            assigned_to = StaffProfile.objects.get(id=serializer.validated_data['assigned_to_id'])

            assignment = OrderAssignment.objects.create(
                order=order,
                assigned_to=assigned_to,
                assigned_by=request.user.staff_profile,
                notes=serializer.validated_data.get('notes', '')
            )

            return Response({
                'status': 'success',
                'message': f'Order assigned to {assigned_to.user.get_full_name()}',
                'assignment': OrderAssignmentSerializer(assignment).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'], permission_classes=[CanAddOrderNotes])
    def add_note(self, request, pk=None):
        """Add a note to the order"""
        order = self.get_object()
        serializer = CreateOrderNoteSerializer(data=request.data)

        if serializer.is_valid():
            note = OrderNote.objects.create(
                order=order,
                created_by=request.user.staff_profile,
                note=serializer.validated_data['note'],
                is_internal=serializer.validated_data.get('is_internal', True)
            )

            return Response({
                'status': 'success',
                'message': 'Note added successfully',
                'note': OrderNoteSerializer(note).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], permission_classes=[CanViewOrderReports])
    def dashboard_stats(self, request):
        """Get dashboard statistics for orders"""
        queryset = self.get_queryset()

        # Calculate various statistics
        total_orders = queryset.count()
        pending_orders = queryset.filter(delivery_status='Pending').count()
        processing_orders = queryset.filter(delivery_status='Processing').count()
        dispatched_orders = queryset.filter(delivery_status='Dispatched').count()
        delivered_orders = queryset.filter(delivery_status='Delivered').count()

        # Orders by payment status
        paid_orders = queryset.filter(payment_status='Paid').count()
        pending_payment = queryset.filter(payment_status='Pending').count()
        failed_payment = queryset.filter(payment_status='Failed').count()

        # Recent orders (last 7 days)
        week_ago = timezone.now() - timezone.timedelta(days=7)
        recent_orders = queryset.filter(placed_at__gte=week_ago).count()

        return Response({
            'total_orders': total_orders,
            'delivery_status': {
                'pending': pending_orders,
                'processing': processing_orders,
                'dispatched': dispatched_orders,
                'delivered': delivered_orders
            },
            'payment_status': {
                'paid': paid_orders,
                'pending': pending_payment,
                'failed': failed_payment
            },
            'recent_orders_7_days': recent_orders
        })

    @action(detail=False, methods=['post'], permission_classes=[CanChangeOrderStatus])
    def bulk_update_status(self, request):
        """Bulk update order status"""
        serializer = BulkOrderStatusUpdateSerializer(data=request.data)

        if serializer.is_valid():
            from .services import OrderService

            try:
                bulk_operation = OrderService.bulk_update_order_status(
                    order_ids=serializer.validated_data['order_ids'],
                    new_status=serializer.validated_data['delivery_status'],
                    user=request.user,
                    reason=serializer.validated_data.get('reason')
                )

                return Response({
                    'status': 'success',
                    'message': 'Bulk status update initiated',
                    'operation_id': str(bulk_operation.operation_id),
                    'operation': BulkOrderOperationSerializer(bulk_operation).data
                })

            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[CanAssignOrders])
    def bulk_assign(self, request):
        """Bulk assign orders to staff member"""
        serializer = BulkOrderAssignmentSerializer(data=request.data)

        if serializer.is_valid():
            from .services import OrderService
            from apps.staff.authorization.models import StaffProfile

            try:
                assigned_to_staff = StaffProfile.objects.get(
                    id=serializer.validated_data['assigned_to_id']
                )

                bulk_operation = OrderService.bulk_assign_orders(
                    order_ids=serializer.validated_data['order_ids'],
                    assigned_to_user=assigned_to_staff.user,
                    assigned_by_user=request.user,
                    notes=serializer.validated_data.get('notes')
                )

                return Response({
                    'status': 'success',
                    'message': 'Bulk assignment initiated',
                    'operation_id': str(bulk_operation.operation_id),
                    'operation': BulkOrderOperationSerializer(bulk_operation).data
                })

            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[CanManageOrders])
    def bulk_generate_documents(self, request):
        """Bulk generate documents (labels, invoices, warehouse docs)"""
        serializer = BulkDocumentGenerationSerializer(data=request.data)

        if serializer.is_valid():
            from .services import OrderService

            try:
                bulk_operation = OrderService.bulk_generate_documents(
                    order_ids=serializer.validated_data['order_ids'],
                    document_types=serializer.validated_data['document_types'],
                    user=request.user,
                    include_customer_invoice=serializer.validated_data.get('include_customer_invoice', False),
                    include_warranty_info=serializer.validated_data.get('include_warranty_info', False)
                )

                return Response({
                    'status': 'success',
                    'message': 'Bulk document generation initiated',
                    'operation_id': str(bulk_operation.operation_id),
                    'operation': BulkOrderOperationSerializer(bulk_operation).data
                })

            except Exception as e:
                return Response({
                    'status': 'error',
                    'message': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def my_assignments(self, request):
        """Get orders assigned to the current staff member"""
        if not hasattr(request.user, 'staff_profile'):
            return Response({'error': 'Staff profile not found'}, status=status.HTTP_400_BAD_REQUEST)

        assigned_orders = Order.objects.filter(
            assignments__assigned_to=request.user.staff_profile,
            assignments__is_active=True
        ).select_related('customer__user').order_by('-placed_at')

        serializer = OrderSummarySerializer(assigned_orders, many=True)
        return Response(serializer.data)


class OrderStatusHistoryViewSet(ReadOnlyModelViewSet):
    """ViewSet for viewing order status history"""
    permission_classes = [IsStaffUser, CanManageOrders]
    serializer_class = OrderStatusHistorySerializer
    pagination_class = StaffPagination

    def get_queryset(self):
        """Get status history for orders the user can access"""
        user = self.request.user

        if user.is_superuser:
            return OrderStatusHistory.objects.all()

        # Get orders the user can access
        accessible_orders = Order.objects.none()
        user_groups = set(user.groups.values_list('name', flat=True))

        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            accessible_orders = Order.objects.all()
        elif STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            accessible_orders = Order.objects.filter(
                delivery_status__in=['Pending', 'Processing']
            )
        elif STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            accessible_orders = Order.objects.filter(
                delivery_status__in=['Pending', 'Processing', 'Dispatched']
            )

        return OrderStatusHistory.objects.filter(
            order__in=accessible_orders
        ).select_related('order', 'changed_by')


class OrderAssignmentViewSet(ModelViewSet):
    """ViewSet for managing order assignments"""
    permission_classes = [IsStaffUser, CanAssignOrders]
    serializer_class = OrderAssignmentSerializer
    pagination_class = StaffPagination

    def get_queryset(self):
        """Get assignments for orders the user can access"""
        user = self.request.user

        if user.is_superuser:
            return OrderAssignment.objects.all()

        # Only Order Managers and Department Heads can view all assignments
        user_groups = set(user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['DEPARTMENT_HEAD']
        }

        if user_groups.intersection(allowed_groups):
            return OrderAssignment.objects.all()

        return OrderAssignment.objects.none()

    def perform_create(self, serializer):
        """Set the assigned_by field to current user"""
        serializer.save(assigned_by=self.request.user.staff_profile)


class OrderNoteViewSet(ModelViewSet):
    """ViewSet for managing order notes"""
    permission_classes = [IsStaffUser, CanAddOrderNotes]
    serializer_class = OrderNoteSerializer
    pagination_class = StaffPagination

    def get_queryset(self):
        """Get notes for orders the user can access"""
        user = self.request.user

        # Get orders the user can access based on their role
        accessible_orders = Order.objects.none()
        user_groups = set(user.groups.values_list('name', flat=True))

        if user.is_superuser or STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            accessible_orders = Order.objects.all()
        elif STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            accessible_orders = Order.objects.filter(
                delivery_status__in=['Pending', 'Processing']
            )
        elif STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            accessible_orders = Order.objects.filter(
                delivery_status__in=['Pending', 'Processing', 'Dispatched']
            )
        elif STAFF_GROUPS['CUSTOMER_SERVICE'] in user_groups:
            accessible_orders = Order.objects.all()

        return OrderNote.objects.filter(
            order__in=accessible_orders
        ).select_related('order', 'created_by')

    def perform_create(self, serializer):
        """Set the created_by field to current user"""
        serializer.save(created_by=self.request.user.staff_profile)


class BulkOrderOperationViewSet(ReadOnlyModelViewSet):
    """
    ViewSet for tracking bulk order operations
    """
    serializer_class = BulkOrderOperationSerializer
    permission_classes = [IsStaffUser, CanManageOrders]
    pagination_class = StaffPagination

    def get_queryset(self):
        """Return bulk operations for the current user or all if admin"""
        if self.request.user.is_superuser:
            return BulkOrderOperation.objects.all()
        return BulkOrderOperation.objects.filter(staff_user=self.request.user)

    @action(detail=True, methods=['get'])
    def progress(self, request, pk=None):
        """Get real-time progress of a bulk operation"""
        operation = self.get_object()
        return Response({
            'operation_id': str(operation.operation_id),
            'status': operation.status,
            'progress_percentage': operation.progress_percentage,
            'processed_items': operation.processed_items,
            'failed_items': operation.failed_items,
            'total_items': operation.total_items,
            'results': operation.results
        })


class OrderDocumentViewSet(ReadOnlyModelViewSet):
    """
    ViewSet for order documents (labels, invoices, warehouse docs)
    """
    serializer_class = OrderDocumentSerializer
    permission_classes = [IsStaffUser, CanManageOrders]
    pagination_class = StaffPagination

    def get_queryset(self):
        """Return documents with filtering options"""
        queryset = OrderDocument.objects.select_related(
            'order', 'generated_by__user', 'bulk_operation'
        )

        # Filter by order if specified
        order_id = self.request.query_params.get('order')
        if order_id:
            queryset = queryset.filter(order_id=order_id)

        # Filter by document type if specified
        doc_type = self.request.query_params.get('document_type')
        if doc_type:
            queryset = queryset.filter(document_type=doc_type)

        # Filter by bulk operation if specified
        bulk_op_id = self.request.query_params.get('bulk_operation')
        if bulk_op_id:
            queryset = queryset.filter(bulk_operation_id=bulk_op_id)

        return queryset.order_by('-generated_at')

    @action(detail=True, methods=['post'])
    def mark_printed(self, request, pk=None):
        """Mark document as printed"""
        document = self.get_object()
        document.mark_printed()

        return Response({
            'status': 'success',
            'message': 'Document marked as printed',
            'printed_at': document.printed_at
        })

    @action(detail=True, methods=['get'])
    def download(self, request, pk=None):
        """Download individual document file"""
        document = self.get_object()

        if not document.file_path:
            return Response({
                'status': 'error',
                'message': 'Document file not found'
            }, status=status.HTTP_404_NOT_FOUND)

        try:
            import os
            from django.conf import settings
            from django.http import FileResponse, Http404

            file_path = os.path.join(settings.MEDIA_ROOT, document.file_path)

            if not os.path.exists(file_path):
                return Response({
                    'status': 'error',
                    'message': 'Physical file not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Return file for download
            response = FileResponse(
                open(file_path, 'rb'),
                as_attachment=True,
                filename=os.path.basename(file_path)
            )
            return response

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error downloading file: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def download_bulk(self, request):
        """Download multiple documents as a ZIP file"""
        document_ids = request.query_params.get('document_ids', '').split(',')
        if not document_ids or document_ids == ['']:
            return Response({
                'status': 'error',
                'message': 'No document IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        documents = self.get_queryset().filter(id__in=document_ids)

        try:
            import zipfile
            import tempfile
            import os
            from django.conf import settings
            from django.http import FileResponse

            # Create temporary ZIP file
            temp_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')

            with zipfile.ZipFile(temp_zip.name, 'w') as zip_file:
                for document in documents:
                    if document.file_path:
                        file_path = os.path.join(settings.MEDIA_ROOT, document.file_path)
                        if os.path.exists(file_path):
                            # Add file to ZIP with a descriptive name
                            zip_name = f"order_{document.order.id}_{document.get_document_type_display()}.{file_path.split('.')[-1]}"
                            zip_file.write(file_path, zip_name)

            # Return ZIP file for download
            response = FileResponse(
                open(temp_zip.name, 'rb'),
                as_attachment=True,
                filename=f'order_documents_{datetime.now().strftime("%Y%m%d_%H%M%S")}.zip'
            )

            # Clean up temp file after response
            def cleanup():
                try:
                    os.unlink(temp_zip.name)
                except:
                    pass

            response.close = cleanup
            return response

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error creating ZIP file: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def print_documents(self, request):
        """Send documents to printer (if printer service is configured)"""
        document_ids = request.data.get('document_ids', [])
        printer_name = request.data.get('printer_name', 'default')

        if not document_ids:
            return Response({
                'status': 'error',
                'message': 'No document IDs provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        documents = self.get_queryset().filter(id__in=document_ids)

        try:
            from .services import OrderService

            # Send documents to printer
            print_results = OrderService.print_documents(documents, printer_name)

            # Mark documents as printed
            for document in documents:
                document.mark_printed()

            return Response({
                'status': 'success',
                'message': f'Sent {documents.count()} documents to printer',
                'results': print_results
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error printing documents: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def available_printers(self, request):
        """Get list of available printers"""
        try:
            from .services import OrderService

            printers = OrderService.get_available_printers()

            return Response({
                'status': 'success',
                'printers': printers,
                'default_printer': printers[0] if printers else None
            })

        except Exception as e:
            return Response({
                'status': 'error',
                'message': f'Error getting printers: {str(e)}',
                'printers': ['default']
            })
