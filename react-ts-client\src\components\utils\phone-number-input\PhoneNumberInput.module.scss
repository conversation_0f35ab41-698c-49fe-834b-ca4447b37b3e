@import '../../../scss/variables';
@import '../../../scss/mixins';

.phone_input_container {
  margin: 0 !important;
  width: 100%;
}

.phone_input_button {
  margin: 0 !important;
  @include flexbox(flex-start, center);
  padding: 0 8px;
  border-right: 1px solid #d1d5db;

  .selected-flag {
    margin: 0 !important;
    background-color: #0091cf !important;
  }
}

.phone_input {
  width: 100% !important;
  border: .1px solid $primary-dark-text-color;
  border-radius: 3px;
  padding: 5px 5px;
  font-size: 16.5px;

  &:focus {
    outline: 2px solid $lighten-blue;
    border: none;
  }

  // width: 100% !important;
  // padding: 8px;
  // border: 1px solid #d1d5db;
  // border-radius: 5px;
  // box-sizing: border-box;

  // &:focus {
  //   border-color: #0091cf;
  //   outline: none;
  // }
}

.phone_input_dropdown {
  border-radius: 5px;
  border: 1px solid #d1d5db;
  max-height: 200px;
  overflow-y: auto;
}