import { Link } from 'react-router-dom'
import StarRating from '../utils/StarRating'
import styles from './Reviews.module.scss'


interface Reviews {
  id: number
  title: string
  description: string
  rating: number
  customer: {
    id: number
    first_name: string
    last_name: string
  }
}

interface Props {
  reviews: Reviews[]
  slug: string
}


const Reviews = ({ reviews, slug }: Props) => {

  console.log(reviews.map((r) => r.customer))

  return (
    <>
      {reviews?.map((review) => (
        <div key={review.id} className={styles.review}>
          <div className={styles.review__user}>
            <img src="" alt="" />
            <p>{review.customer?.first_name} {review.customer?.last_name}</p>
          </div>
          <div className={styles.review__content}>
            <div>
              <StarRating rating={review.rating} color='#00b3ff' />
              <Link to={`/products/${slug}/${review.id}`}>{review.title}</Link>
            </div>
            <div>
              <p>{review.description}</p>
            </div>
          </div>
        </div>
      ))}
    </>
  )
}
export default Reviews