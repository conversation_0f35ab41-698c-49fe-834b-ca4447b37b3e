import SyncLoader from 'react-spinners/SyncLoader'
import Underlay from '../underlay/Underlay'

interface Props {
  loading: boolean
  color?: string
  // override?: string
  size?: number
  bgOpacity?: number
}

const Spinner = ({ loading, color, size = 150, bgOpacity }: Props) => {
  return (
    <Underlay isOpen={loading} bgOpacity={bgOpacity}>
      <SyncLoader
        color={color}
        loading={loading}
        // cssOverride={override}
        size={size}
        aria-label="Loading Spinner"
      />
    </Underlay>
  )
}

export default Spinner