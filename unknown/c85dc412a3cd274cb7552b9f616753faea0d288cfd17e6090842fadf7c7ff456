// import { CSSProperties } from "react"
import SyncLoader from 'react-spinners/SyncLoader'

interface Props {
  loading: boolean
  color?: string
  size?: number
}

const SimpleSpinner = ({ loading, color, size = 150 }: Props) => {
  // const override: CSSProperties = {
  //   position: "relative",
  // }

  return (
    // <div style={{ border: '1px solid black', padding: '20px', position: 'relative' }}>
    <SyncLoader
      color={color}
      loading={loading}
      // cssOverride={override}
      size={size}
      aria-label="Loading Spinner"
    />
    // </div>
  )
}

export default SimpleSpinner
