import moment from "moment"
import { FiTrash2 } from "react-icons/fi"
import { Link } from "react-router-dom"
import useDeleteOrder from "../../../react-query/hooks/order-hooks/useDeleteOrder"
import { OrderShape } from "../../../types/order-types"
import Modal from "../../utils/modal/Modal"
import { addDays } from "../../utils/utils"
import styles from "./OrdersTableView.module.scss"


interface Props {
  orders: OrderShape[]
}

const OrdersTableView = ({ orders }: Props) => {

  const { isModalOpen, handleDeleteClick, confirmDelete, cancelDelete } = useDeleteOrder()

  return (
    <div className={styles.table}>
      <table className={styles.table__orders}>
        <thead>
          <tr>
            <th>Order ID</th>
            <th>Items</th>
            <th>Total</th>
            <th>Delivery Status</th>
            <th>Payment Status</th>
            <th>Order Placed</th>
            <th>Expected Delivery</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          {orders?.map((order) => (
            <tr key={order.id}>
              <td>#{order.id}</td>
              <td>{order.ordered_items.length}</td>
              <td>${order.total.toFixed(2)}</td>
              <td>
                <span className={`${styles.status} ${styles[order.delivery_status]}`}>
                  {order.delivery_status}
                </span>
              </td>
              <td>
                <span className={`${styles.status} ${styles[order.payment_status]}`}>
                  {order.payment_status}
                </span>
              </td>
              <td>{moment(order.placed_at).format('MMM D, YYYY')}</td>
              {/* By default, the delivery date is set to 10 days from the order date. */}
              <td>{addDays(order.placed_at, 10)}</td>
              <td className={styles.action}>
                <Link to={`/checkout/order/${order.id}`} className={styles.viewButton}>View Order</Link>
                <button title='cancel order' onClick={() => handleDeleteClick(order.id)}>
                  <i><FiTrash2 /></i>
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>

      {/* Modal for confirming delete action */}
      <Modal
        show={isModalOpen}
        title='Are you sure you want to delete this order?'
        message="
        Please note that you can only delete an order if it's currently in a PENDING delivery state.
        If you've already paid for your order and wish to cancel it, please contact our 
        customer support team for assistance with a refund.
        Kindly be aware that only a portion of your order amount may be refundable.”
        "
        onClose={cancelDelete}
        onConfirm={confirmDelete}
      />
    </div>
  )
}

export default OrdersTableView
