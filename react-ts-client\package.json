{"name": "react-ts-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.4.2", "@paypal/react-paypal-js": "^8.3.0", "@stripe/react-stripe-js": "^2.7.3", "@tanstack/query-sync-storage-persister": "^5.79.0", "@tanstack/react-query": "^5.35.1", "@tanstack/react-query-persist-client": "^5.79.0", "axios": "^1.6.8", "braintree-web": "^3.113.0", "date-fns": "^4.1.0", "dompurify": "^3.1.7", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.51.5", "react-icons": "^5.2.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.23.0", "react-spinners": "^0.13.8", "use-local-storage-state": "^19.4.0", "zod": "^3.23.8", "zustand": "^4.5.2"}, "devDependencies": {"@hookform/devtools": "^4.3.1", "@tanstack/react-query-devtools": "^5.35.1", "@types/braintree-web": "^3.96.16", "@types/dompurify": "^3.0.5", "@types/react": "^18.2.66", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.2.0", "@typescript-eslint/parser": "^7.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.6", "sass": "^1.76.0", "typescript": "^5.2.2", "vite": "^5.2.0"}}