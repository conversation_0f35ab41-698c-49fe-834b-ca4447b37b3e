import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../../services/auth-client"
import { ChangePasswordShape } from '../../../../../pages/auth/change-existing-password/ChangeExistingPassword'


const useSendSetPassword = () => {

  const apiClient = new APIClient(`/change-password/`)

  const mutation = useMutation({
    mutationFn: (data: ChangePasswordShape) => apiClient.post(data)
  })

  return { mutation }
}

export default useSendSetPassword