# 05: RBAC System User Guide (v3.0)

## **Table of Contents**

1. [Introduction to RBAC](#introduction-to-rbac)
2. [Understanding Your Access](#understanding-your-access)
3. [Current System Capabilities](#current-system-capabilities)
4. [Administrator Guide](#administrator-guide)
5. [System Architecture & Implementation](#system-architecture--implementation)
6. [Troubleshooting & FAQ](#troubleshooting--faq)

---

## **Introduction to RBAC**

### **What is RBAC?**

Role-Based Access Control (RBAC) is a security system that controls what you can do in the application based on your **role** and **permissions**. The current implementation uses Django's built-in Group system enhanced with custom models for staff management.

### **Key Concepts**

#### **Roles (Groups)**

Roles are implemented as Django Groups with predefined names and permissions. Current available roles include:

- **Staff Manager (SM)**: Complete staff and system management
- **Department Head (DH)**: Department-level staff management
- **HR Administrator (HRA)**: Staff profile and user management
- **Product Management Executive (PME)**: Product catalog management (planned)
- **Order Management Executive (OME)**: Order processing management (planned)
- **Customer Service Representative (CSR)**: Customer support operations (planned)

#### **Permissions**

Permissions are Django's built-in permission system plus custom permissions. Examples:

- `auth.add_user` - Create new users
- `auth.view_group` - View groups/roles
- `core.can_toggle_staff_status` - Toggle user staff status
- `core.can_manage_staff_roles` - Manage staff role assignments
- `core.can_view_audit_logs` - Access audit logs

#### **How They Work Together**

Users are assigned to Groups (Roles), and Groups have specific Permissions. The system checks permissions dynamically for each action.

### **System Architecture**

```
User → Assigned to Group(s) → Group has Permissions → Permissions allow API Access
```

**Example**: Sarah (User) → Staff Manager (Group) → Can manage users/groups (Permissions) → Access staff management APIs (Actions)

---

## **Understanding Your Access**

### **Checking Your Current Role and Permissions**

#### **Via API**

```bash
GET /api/staff/auth/user/
```

**Response Example**:

```json
{
  "success": true,
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "is_staff": true,
    "is_superuser": false,
    "groups": ["Staff Manager (SM)"],
    "permissions": [
      "auth.add_user",
      "auth.change_user",
      "auth.view_user",
      "core.can_toggle_staff_status",
      "core.can_manage_staff_roles"
    ],
    "staff_profile": {
      "employee_id": "EMP001",
      "department": "PRODUCT",
      "position_title": "Senior Product Manager",
      "manager": null,
      "status": "ACTIVE"
    }
  }
}
```

### **Understanding Permission Names**

Permission names follow Django's pattern: `app_label.action_model`

**Examples**:

- `auth.add_user` = Can create users
- `auth.change_group` = Can modify groups/roles
- `staff.view_staffprofile` = Can view staff profiles
- `core.can_toggle_staff_status` = Can promote users to staff
- `core.can_manage_staff_roles` = Can assign roles to users
- `core.can_view_audit_logs` = Can access audit logs

### **Current Role Implementation**

#### **Staff Management Roles (Implemented)**

- **Staff Manager (SM)**: Complete staff and system management authority
- **Department Head (DH)**: Department-level staff management
- **HR Administrator (HRA)**: Staff profile and user management

#### **Business Function Roles (Planned - Not Yet Implemented)**

- **Product Management Executive (PME)**: Product catalog management
- **Order Management Executive (OME)**: Order processing management
- **Customer Service Representative (CSR)**: Customer support operations

**Note**: Product, Order, and Customer management through staff APIs are planned but not yet implemented.

---

## **Current System Capabilities**

### **Available API Endpoints**

The staff app currently implements authorization/RBAC functionality only. Product, Order, and Customer management endpoints are planned but not yet implemented.

#### **Authentication & User Information**

##### **1. Get Current User Profile**

```bash
GET /api/staff/auth/user/
```

Returns current staff user information including groups, permissions, and staff profile.

##### **2. Get Detailed Permissions**

```bash
GET /api/staff/auth/permissions/
```

Returns detailed permission breakdown by group and individual permissions.

##### **3. Check Specific Permission**

```bash
POST /api/staff/auth/check-permission/
{
  "permission": "auth.add_user"
}
```

Validates if current user has a specific permission.

#### **Group/Role Management**

##### **1. List All Groups/Roles**

```bash
GET /api/staff/groups/
```

##### **2. View Group Details**

```bash
GET /api/staff/groups/{group_id}/
```

##### **3. Create New Group** (Staff Managers only)

```bash
POST /api/staff/groups/
{
  "name": "New Role Name",
  "permission_ids": [1, 2, 3]
}
```

##### **4. View Group Members**

```bash
GET /api/staff/groups/{group_id}/members/
```

##### **5. Add User to Group**

```bash
POST /api/staff/groups/{group_id}/add_member/
{
  "user_id": 123,
  "notes": "Promoted to team lead"
}
```

##### **6. Remove User from Group**

```bash
POST /api/staff/groups/{group_id}/remove_member/
{
  "user_id": 123,
  "notes": "Role change"
}
```

#### **User Management**

##### **1. List All Users**

```bash
GET /api/staff/users/
```

##### **2. View User Details**

```bash
GET /api/staff/users/{user_id}/
```

##### **3. Toggle Staff Status** (Superusers only)

```bash
POST /api/staff/users/{user_id}/toggle_staff/
```

#### **Permission Management**

##### **1. List All Permissions**

```bash
GET /api/staff/permissions/
```

Returns permissions grouped by app/content type.

##### **2. Add Permission to Role**

```bash
POST /api/staff/roles/{role_id}/add_permission/
{
  "permission_codename": "auth.add_user"
}
```

##### **3. Remove Permission from Role**

```bash
POST /api/staff/roles/{role_id}/remove_permission/
{
  "permission_codename": "auth.delete_user"
}
```

---

## **Administrator Guide**

### **Staff Profile Management**

#### **1. List All Staff Profiles**

```bash
GET /api/staff/staff-profiles/
```

**Filter by department**:

```bash
GET /api/staff/staff-profiles/?department=PRODUCT
```

**Search staff**:

```bash
GET /api/staff/staff-profiles/?search=john
```

#### **2. Create Staff Profile**

```bash
POST /api/staff/staff-profiles/
{
  "user": 1,
  "employee_id": "EMP123",
  "department": "PRODUCT",
  "position_title": "Product Specialist",
  "manager": 5,
  "hire_date": "2024-01-15",
  "notes": "New hire from competitor"
}
```

#### **3. Update Staff Status**

```bash
PATCH /api/staff/staff-profiles/{profile_id}/
{
  "status": "INACTIVE"
}
```

**Status Options**:

- `ACTIVE`: Working normally
- `INACTIVE`: Temporarily disabled
- `ON_LEAVE`: On leave but still employed
- `TERMINATED`: No longer with company

#### **4. View Department Summary**

```bash
GET /api/staff/staff-profiles/department_summary/
```

**Response**:

```json
{
  "success": true,
  "department_summary": {
    "PRODUCT": {
      "name": "Product Management",
      "total_staff": 8,
      "managers": 2
    },
    "ORDER": {
      "name": "Order Management",
      "total_staff": 5,
      "managers": 1
    }
  }
}
```

### **Staff User Creation**

#### **1. Create Complete Staff User**

```bash
POST /api/staff/staff-management/create_staff_user/
{
  "email": "<EMAIL>",
  "employee_id": "EMP123",
  "department": "PRODUCT",
  "position_title": "Product Specialist",
  "manager_id": 5,
  "hire_date": "2024-01-15",
  "group_ids": [3, 4],
  "notes": "Experienced hire from competitor"
}
```

This endpoint creates both the User and StaffProfile in a single transaction.

### **Audit and Monitoring**

#### **1. View Audit Logs**

```bash
GET /api/staff/audit/
```

**Filter by action**:

```bash
GET /api/staff/audit/?action=staff_user_created
```

**Filter by date range**:

```bash
GET /api/staff/audit/?start_date=2024-01-01&end_date=2024-01-31
```

**Filter by user**:

```bash
GET /api/staff/audit/?performed_by=1
```

#### **2. View Audit Summary**

```bash
GET /api/staff/audit/summary/
```

Shows activity breakdown by action type and user.

**Available Audit Actions**:

- `group_created`, `group_updated`, `group_deleted`
- `role_created`, `role_updated`, `role_deleted`
- `user_added_to_group`, `user_removed_from_group`
- `permission_granted`, `permission_revoked`
- `staff_user_created`, `staff_profile_created`
- `staff_profile_updated`, `staff_status_changed`
- `user_staff_toggled`
- `unauthorized_access_attempt`, `permission_check_failed`

---

## **System Architecture & Implementation**

### **Current Implementation Status**

#### **✅ Implemented Features**

1. **User Authentication & Authorization**
   - Django's built-in User model with custom permissions
   - Staff status management
   - Group-based role assignments

2. **Staff Profile Management**
   - Extended staff profiles with organizational data
   - Department-based organization
   - Manager-subordinate relationships with circular dependency prevention

3. **Group/Role Management**
   - Enhanced Group model (Role proxy)
   - Dynamic permission assignment
   - Bulk user assignment to groups

4. **Audit System**
   - Comprehensive audit logging
   - Action tracking with IP addresses
   - Audit log filtering and search

5. **Permission System**
   - Custom permissions for staff operations
   - Dynamic permission checking
   - Permission inheritance through groups

#### **🚧 Planned Features (Not Yet Implemented)**

1. **Product Management APIs**
   - Staff product CRUD operations
   - Category and brand management
   - Bulk product operations

2. **Order Management APIs**
   - Staff order processing
   - Order status management
   - Order assignment workflows

3. **Customer Management APIs**
   - Staff customer support operations
   - Customer data management
   - Customer service workflows

### **Technical Architecture**

#### **Models**

- **StaffProfile**: Extended user profile with organizational data
- **Role**: Proxy model for Django Groups with enhanced functionality
- **GroupMembership**: Tracks group assignments with metadata
- **PermissionAudit**: Comprehensive audit logging

#### **Permissions Structure**

**Core App Custom Permissions**:
- `core.can_toggle_staff_status`
- `core.can_manage_staff_roles`
- `core.can_view_audit_logs`

**Django Built-in Permissions**:
- `auth.add_user`, `auth.change_user`, `auth.view_user`
- `auth.add_group`, `auth.change_group`, `auth.delete_group`, `auth.view_group`

#### **Department Structure**

Available departments:
- `PRODUCT` - Product Management
- `ORDER` - Order Management
- `CUSTOMER` - Customer Management
- `CONTENT` - Content Management
- `FINANCE` - Finance & Analytics
- `ADMIN` - Administration
- `IT` - Information Technology

### **API Base URL**

All staff APIs are available under: `/api/staff/`

**Current Endpoints**:
- `/api/staff/auth/` - Authentication & user info
- `/api/staff/groups/` - Group/role management
- `/api/staff/users/` - User management
- `/api/staff/staff-profiles/` - Staff profile management
- `/api/staff/permissions/` - Permission management
- `/api/staff/audit/` - Audit log access

---

## **Troubleshooting & FAQ**

### **Common Issues**

#### **"Permission Denied" Errors**

**Problem**: User can't perform an action
**Solution**:

1. Check user's current permissions: `GET /api/staff/auth/user/`
2. Verify required permission for the action
3. Contact Staff Manager to assign appropriate role

#### **"Circular Management Dependency" Error**

**Problem**: Can't assign manager due to circular dependency
**Solution**:

1. Check management chain: Review who manages whom
2. Break the circle: Temporarily remove conflicting manager assignments
3. Reassign properly: Set up hierarchy without circular references

#### **Can't Create Staff User**

**Problem**: Staff creation fails
**Solution**:

1. Verify you have appropriate permissions (Staff Manager or HR Administrator role)
2. Check if email already exists in system
3. Ensure employee_id is unique
4. Verify manager_id exists and is active staff member

#### **API Endpoint Not Found**

**Problem**: Getting 404 errors for documented endpoints
**Solution**:

1. Verify the endpoint is implemented (check "Current Implementation Status" section)
2. Ensure correct base URL: `/api/staff/`
3. Product/Order/Customer management endpoints are planned but not yet implemented

### **Frequently Asked Questions**

#### **Q: Can a user have multiple roles?**

A: Yes! Users can be assigned to multiple groups/roles, and they'll have the combined permissions of all their roles.

#### **Q: How do I know what permissions a role has?**

A: Use `GET /api/staff/groups/{group_id}/` to see the group details including permissions.

#### **Q: Can I create custom permissions?**

A: Custom permissions need to be added to Django models. The current system has custom permissions in the `core` app. Contact your development team for new permission requirements.

#### **Q: What happens when a staff member leaves?**

A: Change their status to "TERMINATED" in their staff profile, which preserves audit history while indicating they're no longer active.

#### **Q: How do I delegate staff management to department heads?**

A: Assign the "Department Head (DH)" role which includes `core.can_manage_staff_roles` permission for department-level management.

#### **Q: Can I see who made changes to the system?**

A: Yes! All actions are logged in the audit system. Use `GET /api/staff/audit/` to view activity logs with filtering options.

#### **Q: Why can't I access product/order management endpoints?**

A: These endpoints are planned but not yet implemented. Currently, only authorization/RBAC functionality is available in the staff app.

### **Getting Help**

#### **For Users**

- Contact your Department Head for role-related questions
- Contact Staff Manager for access issues
- Check audit logs to understand permission changes

#### **For Administrators**

- Review this guide for current capabilities
- Check audit logs for troubleshooting
- Use Django admin for low-level permission management

#### **For Developers**

- Refer to the staff app implementation in `apps/staff/`
- Check the authorization module for RBAC functionality
- Review audit logs for system behavior analysis

---

**System Version**: v3.0 (Updated)
**Last Updated**: July 2025
**Implementation Status**: Authorization/RBAC Complete, Product/Order/Customer Management Planned
**Contact**: System Administrator
