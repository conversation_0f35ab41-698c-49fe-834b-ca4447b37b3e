import { useEffect, useState } from "react"
import styles from "./SelectableAttributeValues.module.scss"
import cartStore from "../../../zustand/cartStore"

interface Attribute {
  id: number
  title: string
}

interface AttributeValue {
  id: number
  attribute: Attribute
  attribute_value: string
  selectable: boolean
}

interface SelectableAttributeValue {
  id: number
  is_active: boolean
  attribute_value: AttributeValue
  product_variant: number
}

interface Props {
  selectableAttValues: SelectableAttributeValue[]
}

interface GroupedItem {
  id: string
  is_active: boolean
  attribute_title: string
  attribute_value: string[]
}


const SelectableAttributeValues = (props: Props) => {
  const [items, setItems] = useState<GroupedItem[]>([])
  // const [extraData, setExtraData] = useState<Record<string, string>>({})

  const { setExtraData, cartItem } = cartStore()

  // console.log(extraData)


  function groupByAttribute(array: SelectableAttributeValue[]): GroupedItem[] {
    // Create an object to store grouped items
    const groupedItems: Record<string, GroupedItem> = {}

    // Iterate through the array
    if (array) {
      array.forEach(item => {
        const { attribute } = item.attribute_value

        // Check if the attribute id already exists in groupedItems
        if (groupedItems[attribute.id]) {
          // If it exists, push the attribute value to the existing array
          groupedItems[attribute.id].attribute_value.push(item.attribute_value.attribute_value)
        } else {
          // If it doesn't exist, create a new grouped item
          groupedItems[attribute.id] = {
            id: '',
            is_active: true,
            attribute_title: attribute.title,
            attribute_value: [item.attribute_value.attribute_value],
          }
        }
      })
    }

    // Assign id to each grouped item and convert the object into an array
    const result = Object.entries(groupedItems).map(([id, groupedItem]) => ({
      ...groupedItem,
      id,
    }))

    // Return the grouped items
    return result
  }

  useEffect(() => {
    const groupedItems = groupByAttribute(props?.selectableAttValues)
    setItems(groupedItems)
  }, [props])

  const handleAttributeValueChange = (attributeTitle: string, attributeValue: string) => {
    const newExtraData = {
      ...cartStore.getState().cartItem.extra_data,
      [attributeTitle]: attributeValue,
    }
    setExtraData(newExtraData)
    // setExtraDataInStore(extraData)
  }


  return (
    <>
      {items.map((item) => (
        <div key={item.id} className={styles.attribute_value_group}>
          <h3>
            {item.attribute_title}:
            {cartItem.extra_data[item.attribute_title] && (
              <span className={styles.selected_value_display}>
                {cartItem.extra_data[item.attribute_title]}
              </span>
            )}
          </h3>
          <ul>
            {item.attribute_value.map((att_val) => (
              <li key={att_val}>
                <input
                  type="radio"
                  name={item.attribute_title}
                  value={att_val}
                  onChange={() =>
                    handleAttributeValueChange(item.attribute_title, att_val)
                  }
                />
                <label htmlFor={`${item.attribute_title}-${att_val}`}>{att_val}</label>
              </li>
            ))}
          </ul>
        </div>
      ))}
    </>
  )
}

export default SelectableAttributeValues