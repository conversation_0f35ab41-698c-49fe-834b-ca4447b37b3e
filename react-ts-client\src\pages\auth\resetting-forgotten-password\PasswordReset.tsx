import { useState } from 'react'
import PasswordResetRequest, { ResetRequestShape } from './PasswordResetRequest'
import VerifyResetCode from './VerifyResetCode'
import { useNavigate } from 'react-router-dom'
import Alert from '../../../components/utils/alert/Alert'
import Logo from '../../../components/utils/logo/Logo'
import styles from './PasswordReset.module.scss'

const PasswordReset = () => {
  const navigate = useNavigate()
  const [verificationStep, setVerificationStep] = useState(false)
  const [emailOrPhone, setEmailOrPhone] = useState<ResetRequestShape | null>(null)
  const [verificationSuccess, setVerificationSuccess] = useState(false)

  const handleRequestSuccess = (email_or_phone: ResetRequestShape) => {
    setEmailOrPhone(email_or_phone)
    setVerificationStep(true)
  }

  const handleVerificationSuccess = () => {
    // Navigate to success or login page
    setEmailOrPhone(null)
    setVerificationSuccess(true)
    // navigate('/user/login')
  }

  return (
    <>
      {verificationSuccess ? (
        <div className={styles.register_container}>
          <div className={styles.reset_container}>
            <div className='logo_header'>
              <Logo />
            </div>
            <Alert variant='success' message='Password reset successful. You can now login with your new password.' />
            <button
              className={`empty_btn ${styles.login_btn}`}
              onClick={() => navigate('/user/login')}
            >Login
            </button>
          </div>
        </div>
      ) : (
        <div>
          {!verificationStep ? (
            <PasswordResetRequest
              onRequestSuccess={handleRequestSuccess}
            />
          ) : (
            <VerifyResetCode
              emailOrPhone={emailOrPhone!.email_or_phone}
              onVerificationSuccess={handleVerificationSuccess} />
          )}
        </div>
      )}
    </>
  )
}

export default PasswordReset
