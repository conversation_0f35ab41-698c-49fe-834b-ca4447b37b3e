// ContactInfoForm.tsx
import { useState } from 'react'
import { useForm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { contactInfoSchema } from '../../../../types/schemas'
import useSendUpdateAuthInfo from '../../../../react-query/hooks/auth-hooks/update-auth-info/useUpdateAuthInfo'
import Logo from '../../../../components/utils/logo/Logo'
import Alert from '../../../../components/utils/alert/Alert'
import PhoneNumberInput from '../../../../components/utils/phone-number-input/PhoneNumberInput'
import loading_svg from '../../../../assets/loading_svg_white.svg'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import styles from './UpdateAuthInfo.module.scss'

type ContactInfoInputs = z.infer<typeof contactInfoSchema>

interface ContactInfoFormProps {
  username: string | null
  onSuccess: (altUsername: string) => void
  onSkip?: () => void
}

const UpdateAuthContact = ({ username, onSuccess, onSkip }: ContactInfoFormProps) => {
  const [phoneValue, setPhoneValue] = useState('')



  const { register, handleSubmit, formState: { errors }, setValue } = useForm<ContactInfoInputs>({
    resolver: zodResolver(contactInfoSchema)
  })
  const { authInfoMutation } = useSendUpdateAuthInfo()

  const handleInputChange = (value: string) => {
    if (username?.startsWith('+')) {
      // For email input
      setValue("altUsername", value)
    } else {
      // For phone input
      setPhoneValue(value)
      setValue("altUsername", `+${value}`)
    }
  }

  const onSubmit: SubmitHandler<ContactInfoInputs> = async (data) => {
    const submissionData = data.altUsername?.startsWith('+')
      ? { phone_number: data.altUsername }
      : { email: data.altUsername }

    authInfoMutation.mutate(submissionData, {
      onSuccess: (res) => {
        onSuccess(res.username)
      }
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {/* <h2 className='title'>
          {`Add ${username?.startsWith('+') ? 'a phone number' : 'an email'}`}
        </h2> */}

        {authInfoMutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(authInfoMutation.error as AxiosError<ErrorResponse>)}
            textAlign='center'
          />
        )}

        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <Alert
            variant='info'
            message={
              username?.startsWith('+')
                ? 'It is recommended to add an email address as an additional contact.'
                : 'A verified phone number is required to purchase items in this store.'
            }
          />

          <div className='form_group'>
            <label className='form_label' >
              {username?.startsWith('+') ? 'Enter an email:' : 'Enter a phone number:'}
            </label>

            {username?.startsWith('+') ? (
              <input
                className='form_input'
                type="text"
                {...register("altUsername")}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Enter email"
              />
            ) : (
              <PhoneNumberInput
                value={phoneValue}
                defaultCountry="lk"
                onlyCountries={['lk', 'us']}
                onChange={handleInputChange}
                placeholder='Enter phone number'
                pending={authInfoMutation.isPending}
              />
            )}

            {errors.altUsername && (
              <p className='form_error'>{errors.altUsername.message}</p>
            )}
          </div>

          <div className={styles.btn_container_2}>
            <button type="submit" disabled={authInfoMutation.isPending}>
              {authInfoMutation.isPending ? (
                <img src={loading_svg} alt="Loading..." className='loading_svg' />
              ) : 'Submit'}
            </button>

            {username?.startsWith('+') && onSkip && (
              <button className={styles.empty_btn} onClick={onSkip}>
                Skip
              </button>
            )}
          </div>
        </form>
      </div>
    </div>
  )
}

export default UpdateAuthContact