import React from 'react'
import { Link } from 'react-router-dom'
import { ProductShape } from '../../../types/product-types'
import { SimpleRating } from '../../rating/SimpleRating'
import LimitTitleLength from '../../utils/TextLimit'
import styles from './SimpleProductCard.module.scss'
import noImagePlaceholder from '../../../assets/no-image-placeholder.png'
import { findFirstAvailableImage } from '../../../utils/product-utils'

interface Props {
  product: ProductShape
}

const SimpleProductCard: React.FC<Props> = ({ product }) => {
  const { title, slug, product_variant } = product
  const discountPercentage = 25 // 25% discount

  // Find the first available image across all variants
  const imageUrl = findFirstAvailableImage(product, 'https://res.cloudinary.com/dev-kani')

  return (
    <div className={styles.product_card}>
      <Link to={`/products/${slug}/`}>
        <div className={styles.product_card__image}>
          <img
            src={imageUrl || noImagePlaceholder}
            alt={title}
          />
          {/* <div className={styles.discount}>-{discountPercentage}%</div> */}
        </div>
        <div className={styles.product_card__info}>
          <h3><LimitTitleLength title={title} maxLength={38} /></h3>
          <div className={styles.rating}>
            <SimpleRating product={product} />
            <span>|</span>
            <span>50 sold</span>
          </div>
          <div className={styles.price}>
            <span>${product_variant[0]?.price}</span>
            <span>
              ${(product_variant[0]?.price / (1 - discountPercentage / 100)).toFixed(2)}
            </span>
          </div>
        </div>
      </Link>
    </div>
  )
}

export default SimpleProductCard