import { <PERSON> } from "react-router-dom"
import { <PERSON><PERSON><PERSON>, FiMinus, FiTrash2 } from "react-icons/fi"
import { CartItemShape } from "../../../types/store-types"
import LimitTitleLength from "../../../components/utils/TextLimit"
import styles from './CartItemsList.module.scss'
import noImagePlaceholder from "../../../assets/no-image-placeholder.png"


interface CartItemsListProps {
  cartItems: CartItemShape[]
  handleIncrement?: (item: CartItemShape) => void
  handleDecrement?: (item: CartItemShape) => void
  deleteCartItem?: (itemId: number) => void
}

const CartItemsList = ({ cartItems, handleIncrement, handleDecrement, deleteCartItem }: CartItemsListProps) => {
  return (
    <ul>
      {cartItems?.map((item: CartItemShape) => (
        <li key={item.id} className={styles.cart_item}>
          <div className={styles.cart_item__img}>
            <img
              src={item.product_variant?.product_image?.[0]?.image
                ? `${import.meta.env.VITE_CLOUDINARY_URL}/${item.product_variant.product_image[0].image}`
                : noImagePlaceholder}
              alt={item.product_variant?.product_image?.[0]?.alternative_text || item.product.title}
            />
          </div>
          <div className={styles.cart_item__info}>
            <span className={styles.cart_item__title}>
              <Link to={`/products/${item.product.slug}/`}>
                <LimitTitleLength title={item.product.title} maxLength={60} />
              </Link>
            </span>{` `}
            <span>(${item.product_variant.price} x {item.quantity})</span>
            <span>variant: {item.product_variant?.price_label?.attribute_value}</span>
            {Object.entries(item.extra_data).map(([key, value], index) => (
              <div key={index} className={styles.cart_item__extra_data}>
                <p>{key} :</p><p>{value}</p>
              </div>
            ))}
          </div>
          <div className={styles.cart_item__quantity}>
            <div>
              <p>Qty:</p>
              {handleDecrement && <button
                onClick={() => handleDecrement(item)}
                disabled={item.product_variant.stock_qty === 0}><i><FiMinus /></i></button>}
              <p>{item.quantity}</p>
              {handleIncrement && <button
                onClick={() => handleIncrement(item)}
                disabled={item.product_variant.stock_qty === 0}><i><FiPlus /></i></button>}
              {deleteCartItem && <button onClick={() => deleteCartItem(item.id)}><i><FiTrash2 /></i></button>}
            </div>
            {item.product_variant.stock_qty === 0 && <p>Out of Stock</p>}
          </div>

        </li>
      ))}
    </ul>
  )
}

export default CartItemsList
