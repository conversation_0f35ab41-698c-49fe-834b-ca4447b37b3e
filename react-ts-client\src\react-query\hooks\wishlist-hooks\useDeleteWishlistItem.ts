import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { WISHLIST_ITEMS } from "../constants"

const useDeleteWishlistItem = () => {
  const queryClient = useQueryClient()
  const apiClient = new APIClient('/wishlist')

  const mutation = useMutation({
    mutationFn: (itemId: number) => {
      // This will correctly construct the URL as /wishlist/123/
      return apiClient.delete(itemId)
    },
    onSuccess: () => {
      // Invalidate the wishlist query to refetch the latest data
      queryClient.invalidateQueries({
        queryKey: [WISHLIST_ITEMS]
      })
    },
    onError: (error) => {
      // Optional: Add error handling
      console.error('Failed to delete wishlist item:', error)
    }
  })

  return mutation
}

export default useDeleteWishlistItem