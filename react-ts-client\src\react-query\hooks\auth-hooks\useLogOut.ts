import { useMutation, useQueryClient } from '@tanstack/react-query'
import authStore from "../../../zustand/authStore"
import APIClient from "../../services/auth-client"
import { CUSTOMER_DETAILS } from '../constants'

const useLogout = () => {
  const { setIsLoggedIn } = authStore()
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`users/logout/`)

  const mutation = useMutation({
    mutationFn: () => apiClient.post(),
    onSuccess: () => {
      console.log('Logout was success')
      setIsLoggedIn(false)
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS],
      })
      // queryClient.refetchQueries({
      //   queryKey: [CUSTOMER_DETAILS]
      // })
      queryClient.removeQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}

export default useLogout