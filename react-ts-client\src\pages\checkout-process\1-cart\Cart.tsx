import { useNavigate } from "react-router-dom"
import { useEffect, useState } from "react"
import useCart from "../../../react-query/hooks/cart-hooks/useCart"
import useUpdateCart from "../../../react-query/hooks/cart-hooks/useUpdateCart"
import useDeleteCartItem from "../../../react-query/hooks/cart-hooks/useDeleteCartItem"
import Spinner from "../../../components/utils/spinner/Spinner"
import Alert from "../../../components/utils/alert/Alert"
import { CartItemShape } from "../../../types/store-types"
import EmptyCart from "../../../components/utils/empty-cart/EmptyCart"
import cartStore from "../../../zustand/cartStore"
import CartItemsList from '../../../components/checkout/cart/CartItemsList'
import styles from './Cart.module.scss'
import PriceSummary from "../../../components/checkout/price-summary/PriceSummary"
import { getErrorMessage } from "../../../components/utils/getErrorMessage"
import { ErrorResponse } from "../../../types/types"
import { AxiosError } from "axios"

const Cart = () => {
  const navigate = useNavigate()
  const [noStockAlert, setNoStockAlert] = useState(false)

  const { cartId } = cartStore()
  const { isPending, error, data } = useCart()
  const { handleQuantityUpdate, mutation } = useUpdateCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const handleIncrement = (item: CartItemShape) => {
    const newQuantity = item.quantity + 1
    handleQuantityUpdate(item.id, newQuantity)
  }

  const handleDecrement = (item: CartItemShape) => {
    if (item.quantity > 1) {
      const newQuantity = item.quantity - 1
      handleQuantityUpdate(item.id, newQuantity)
    }
  }

  const handleCheckout = (cartItems: CartItemShape[]) => {
    const hasOutOfStockItems = cartItems.some(item => item.product_variant.stock_qty === 0)
    if (hasOutOfStockItems) {
      setNoStockAlert(true)
    } else {
      navigate('/checkout')
    }
  }

  useEffect(() => {
    // When cart data changes, check if there are still any out-of-stock items.
    if (data && data?.cart_items?.length > 0) {
      const stillOutOfStock = data?.cart_items.some(item => item.product_variant.stock_qty === 0)
      // Hide the alert if no items are out of stock.
      if (!stillOutOfStock) {
        setNoStockAlert(false)
      }
    }
  }, [data])

  return (
    <div>
      {noStockAlert && <Alert variant="error" message={`
      Some items are out of stock. Please remove them from the cart to proceed with checkout process.`} />}

      {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}

      {!cartId ? <EmptyCart /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
          error ? <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} /> :
            (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) ?
              <EmptyCart /> :
              <>
                <div className={`${styles.cart} container`}>
                  <h2>Shopping Cart</h2>
                  <div className={styles.cart__cart_items}>
                    <CartItemsList
                      cartItems={data.cart_items}
                      handleIncrement={handleIncrement}
                      handleDecrement={handleDecrement}
                      deleteCartItem={deleteCartItem}
                    />
                    <PriceSummary
                      totalPrice={data?.total_price}
                      shippingCost={data?.shipping_cost}
                      grandTotal={data?.grand_total}
                      onCheckout={() => handleCheckout(data?.cart_items)}
                      cart_weight={data?.cart_weight}
                    />
                  </div>
                </div>
              </>
      }
    </div>
  )

}
export default Cart
