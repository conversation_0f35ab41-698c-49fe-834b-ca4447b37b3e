@import '../../../scss/variables';
@import '../../../scss/mixins';


.toggle_view {
  @include flexbox(flex-end, center);
  column-gap: 6px;
  padding: .5rem 0;

  svg {
    font-size: $font-size-3;
    padding: 2px;
    color: $primary-dark-blue;
    cursor: pointer;

    &:hover {
      color: $primary-lighter-text-color;
    }

    // Increase specificity for .active
    &.active {
      box-shadow: $box-shadow-blue-1;
      color: $primary-blue;
    }
  }
}