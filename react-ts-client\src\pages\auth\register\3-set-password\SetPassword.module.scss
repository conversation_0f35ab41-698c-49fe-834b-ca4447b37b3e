@import '../../../../scss/variables';
@import '../../../../scss/mixins';
// @import '../../../../scss/animations.scss';

.new_password {
  padding: $padding-5;
  border-radius: $border-radius-1;
  margin: 2rem 0 0 0;
  width: 400px;
  align-self: flex-start;
  background-color: #fff;

  // @include slideInAnimation(0.8s, ease-out);
}

.btn_container {
  // background-color: rgb(132, 189, 189);
  @include flexbox(center, center);
  column-gap: 1rem;
}