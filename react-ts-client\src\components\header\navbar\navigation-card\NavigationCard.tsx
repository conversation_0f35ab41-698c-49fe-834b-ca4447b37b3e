import { Link } from 'react-router-dom'
import { Category } from '../../../../types/product-types' // Import existing Category interface
import styles from './NavigationCard.module.scss'
import Alert from '../../../utils/alert/Alert'
import { getErrorMessage } from '../../../utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'


interface Props {
  isPending: boolean
  error: unknown
  categories: Category[]
  isOpen: boolean
  setIsOpen: (isOpen: boolean) => void
}

// Utility function to build a hierarchical structure of categories
const buildHierarchy = (categories: Category[]): Category[] => {
  const categoryMap = new Map<number, Category>() // Map to hold categories by their IDs

  // Initialize the map with empty children arrays
  categories.forEach(cat =>
    categoryMap.set(cat.id, { ...cat, children: [] })
  )

  const rootCategories: Category[] = [] // Array to hold top-level categories

  categories.forEach(cat => {
    if (cat.parent === null) {
      rootCategories.push(categoryMap.get(cat.id)!) // Add top-level categories (parent is null) to rootCategories
    } else {
      const parentCategory = categoryMap.get(cat.parent)
      if (parentCategory) {
        parentCategory.children!.push(categoryMap.get(cat.id)!) // Add the current category to its parent's children array
      }
    }
  })

  return rootCategories // Return the hierarchical structure
}

// Main functional component for the navigation card
const NavigationCard = ({ isPending, error, categories, setIsOpen }: Props) => {
  const hierarchicalCategories = buildHierarchy(categories) // Build hierarchical structure of categories

  // Recursive function to render categories and their children
  const renderCategories = (items: Category[], level: number = 0) => (
    <ul className={`${styles.list} ${styles[`level-${level}`]}`}>
      {/* Apply level-specific styles using SCSS classes */}
      {items.map(item => ( // Iterate over each category
        <li key={item.id}>
          <Link
            to={`category/${item.slug}`} // Link to the category's page
            onClick={() => setIsOpen(false)} // Close navigation card on link click
          >
            {item.title} {/* Display the category title */}
          </Link>
          {item.children && item.children.length > 0 && renderCategories(item.children, level + 1)}
          {/* Recursively render children categories */}
        </li>
      ))}
    </ul>
  )

  return (
    <div
      onMouseLeave={() => setIsOpen(false)} // Close navigation card on mouse leave
      onMouseOver={() => setIsOpen(true)} // Keep navigation card open on hover
      className={`${styles.nav__card} container`} // Apply styling classes
    >
      {isPending ? (
        <div>Loading...</div> // Show loading state if data is still fetching
      ) : error ? (
        <Alert variant="error" message={getErrorMessage(error as AxiosError<ErrorResponse>)} />
        // Show an error alert if an error occurs
      ) : (
        renderCategories(hierarchicalCategories)
        // Render the category hierarchy if no errors and not loading
      )}
    </div>
  )
}

export default NavigationCard
