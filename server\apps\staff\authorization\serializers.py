from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from .models import GroupMembership, PermissionAudit, Role, StaffProfile
from apps.customers.models import Customer

User = get_user_model()


class PermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for Django Permission model
    Used to display available permissions
    """
    app_label = serializers.CharField(source='content_type.app_label', read_only=True)
    model = serializers.CharField(source='content_type.model', read_only=True)

    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'app_label', 'model']
        read_only_fields = ['id', 'name', 'codename', 'app_label', 'model']


class RoleSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Role proxy model with additional functionality
    """
    permissions = PermissionSerializer(many=True, read_only=True)
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of permission IDs to assign to this role"
    )
    member_count = serializers.ReadOnlyField()
    staff_member_count = serializers.ReadOnlyField()
    permission_count = serializers.ReadOnlyField()
    permission_summary = serializers.SerializerMethodField()

    class Meta:
        model = Role
        fields = [
            'id', 'name', 'permissions', 'permission_ids',
            'member_count', 'staff_member_count', 'permission_count',
            'permission_summary'
        ]
        read_only_fields = ['id', 'permissions', 'member_count', 'staff_member_count', 'permission_count']

    def get_permission_summary(self, obj):
        """Get permission summary grouped by app"""
        return obj.get_permission_summary()

    def create(self, validated_data):
        """Create role with permissions"""
        permission_ids = validated_data.pop('permission_ids', [])
        role = Role.objects.create(**validated_data)

        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids)
            role.permissions.set(permissions)

        return role

    def update(self, instance, validated_data):
        """Update role with permissions"""
        permission_ids = validated_data.pop('permission_ids', None)

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update permissions if provided
        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids)
            instance.permissions.set(permissions)

        return instance


class GroupSerializer(serializers.ModelSerializer):
    """
    Serializer for Django Group model with permissions and member count
    """
    permissions = PermissionSerializer(many=True, read_only=True)
    permission_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="List of permission IDs to assign to this group"
    )
    member_count = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions', 'permission_ids', 'member_count']
        read_only_fields = ['id', 'permissions', 'member_count']

    def get_member_count(self, obj):
        """Get the number of active members in this group"""
        return obj.user_set.filter(is_active=True).count()

    def create(self, validated_data):
        """Create group with permissions"""
        permission_ids = validated_data.pop('permission_ids', [])
        group = Group.objects.create(**validated_data)

        if permission_ids:
            permissions = Permission.objects.filter(id__in=permission_ids)
            group.permissions.set(permissions)

        return group

    def update(self, instance, validated_data):
        """Update group with permissions"""
        permission_ids = validated_data.pop('permission_ids', None)

        # Update basic fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update permissions if provided
        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids)
            instance.permissions.set(permissions)

        return instance


class StaffProfileSerializer(serializers.ModelSerializer):
    """
    Serializer for StaffProfile model
    """
    full_name = serializers.ReadOnlyField()
    is_manager = serializers.ReadOnlyField()
    team_size = serializers.ReadOnlyField()
    user_email = serializers.CharField(source='user.email', read_only=True)
    manager_name = serializers.CharField(source='manager.full_name', read_only=True)

    class Meta:
        model = StaffProfile
        fields = [
            'id', 'user', 'user_email', 'employee_id', 'department',
            'position_title', 'manager', 'manager_name', 'hire_date',
            'status', 'notes', 'full_name', 'is_manager', 'team_size',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'full_name', 'is_manager', 'team_size']

    def validate_employee_id(self, value):
        """Validate employee ID uniqueness"""
        if self.instance and self.instance.employee_id == value:
            return value

        if StaffProfile.objects.filter(employee_id=value).exists():
            raise serializers.ValidationError("Employee ID already exists")
        return value

    def validate(self, data):
        """Validate staff profile data"""
        if 'manager' in data and 'user' in data:
            if data['manager'] and data['manager'].user == data['user']:
                raise serializers.ValidationError("Staff member cannot be their own manager")
        return data


class StaffUserCreateSerializer(serializers.Serializer):
    """
    Serializer for creating new staff users with profile
    """
    email = serializers.EmailField()
    phone_number = serializers.CharField(required=False, allow_blank=True)
    employee_id = serializers.CharField(max_length=20)
    department = serializers.ChoiceField(choices=StaffProfile.DEPARTMENT_CHOICES)
    position_title = serializers.CharField(max_length=100)
    manager_id = serializers.IntegerField(required=False, allow_null=True)
    hire_date = serializers.DateField()
    notes = serializers.CharField(required=False, allow_blank=True)
    role_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        help_text="List of role IDs to assign to the new staff user"
    )

    def validate_email(self, value):
        """Validate email uniqueness"""
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError("User with this email already exists")
        return value

    def validate_employee_id(self, value):
        """Validate employee ID uniqueness"""
        if StaffProfile.objects.filter(employee_id=value).exists():
            raise serializers.ValidationError("Employee ID already exists")
        return value

    def validate_manager_id(self, value):
        """Validate manager exists and has staff profile"""
        if value:
            try:
                manager_profile = StaffProfile.objects.get(id=value)
                if manager_profile.status != 'ACTIVE':
                    raise serializers.ValidationError("Manager must be active")
            except StaffProfile.DoesNotExist:
                raise serializers.ValidationError("Manager not found")
        return value

    def create(self, validated_data):
        """Create user with staff profile and role assignments"""
        role_ids = validated_data.pop('role_ids', [])
        manager_id = validated_data.pop('manager_id', None)

        # Create user
        user_data = {
            'email': validated_data['email'],
            'is_staff': True,
            'is_active': True
        }
        if validated_data.get('phone_number'):
            user_data['phone_number'] = validated_data['phone_number']

        user = User.objects.create_user(**user_data)

        # Create staff profile
        profile_data = {
            'user': user,
            'employee_id': validated_data['employee_id'],
            'department': validated_data['department'],
            'position_title': validated_data['position_title'],
            'hire_date': validated_data['hire_date'],
            'notes': validated_data.get('notes', ''),
        }

        if manager_id:
            profile_data['manager'] = StaffProfile.objects.get(id=manager_id)

        staff_profile = StaffProfile.objects.create(**profile_data)

        # Assign roles
        if role_ids:
            roles = Role.objects.filter(id__in=role_ids)
            user.groups.set(roles)

        return {
            'user': user,
            'staff_profile': staff_profile,
            'roles_assigned': len(role_ids)
        }


class UserBasicSerializer(serializers.ModelSerializer):
    """
    Basic user serializer for listing and selection
    """
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'email', 'is_active', 'is_staff', 'is_superuser',
            'full_name', 'date_joined', 'last_login'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_full_name(self, obj):
        """Get user's full name from Customer model if available"""
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except (AttributeError, Customer.DoesNotExist):
            return obj.email.split('@')[0]  # Use email prefix as fallback


class GroupMembershipSerializer(serializers.ModelSerializer):
    """
    Serializer for GroupMembership model
    """
    user = UserBasicSerializer(read_only=True)
    group = GroupSerializer(read_only=True)
    assigned_by_email = serializers.CharField(source='assigned_by.email', read_only=True)

    class Meta:
        model = GroupMembership
        fields = [
            'id', 'user', 'group', 'assigned_by_email',
            'assigned_at', 'is_active', 'notes'
        ]
        read_only_fields = ['id', 'assigned_at']


class UserGroupAssignmentSerializer(serializers.Serializer):
    """
    Serializer for assigning users to groups
    """
    user_id = serializers.IntegerField()
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_user_id(self, value):
        """Validate that the user exists and is active"""
        try:
            user = User.objects.get(id=value)
            if not user.is_active:
                raise serializers.ValidationError("User account is not active")
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError("User not found")


class BulkUserGroupAssignmentSerializer(serializers.Serializer):
    """
    Serializer for bulk user group assignments
    """
    user_ids = serializers.ListField(
        child=serializers.IntegerField(),
        min_length=1,
        max_length=50,
        help_text="List of user IDs to assign to the group"
    )
    notes = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_user_ids(self, value):
        """Validate that all users exist and are active"""
        users = User.objects.filter(id__in=value, is_active=True)
        if users.count() != len(value):
            raise serializers.ValidationError("Some users not found or are not active")
        return value


class UserDetailSerializer(serializers.ModelSerializer):
    """
    Detailed user serializer with groups, permissions, and staff profile
    """
    groups = GroupSerializer(many=True, read_only=True)
    user_permissions = PermissionSerializer(many=True, read_only=True)
    all_permissions = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()
    staff_profile = StaffProfileSerializer(read_only=True)

    class Meta:
        model = User
        fields = [
            'id', 'email', 'is_active', 'is_staff', 'is_superuser',
            'full_name', 'date_joined', 'last_login',
            'groups', 'user_permissions', 'all_permissions', 'staff_profile'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_all_permissions(self, obj):
        """Get all permissions (from groups and direct assignments)"""
        return list(obj.get_all_permissions())

    def get_full_name(self, obj):
        """Get user's full name from Customer or StaffProfile model if available"""
        # Try staff profile first
        try:
            return obj.staff_profile.full_name
        except (AttributeError, StaffProfile.DoesNotExist):
            pass

        # Fall back to customer profile
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except (AttributeError, Customer.DoesNotExist):
            return obj.email.split('@')[0]  # Use email prefix as fallback


class PermissionAuditSerializer(serializers.ModelSerializer):
    """
    Serializer for PermissionAudit model
    """
    performed_by_email = serializers.CharField(source='performed_by.email', read_only=True)
    target_user_email = serializers.CharField(source='target_user.email', read_only=True)
    target_group_name = serializers.CharField(source='target_group.name', read_only=True)

    class Meta:
        model = PermissionAudit
        fields = [
            'id', 'action', 'performed_by_email', 'target_user_email',
            'target_group_name', 'details', 'ip_address', 'timestamp'
        ]
        read_only_fields = ['id', 'timestamp']


class AuthUserSerializer(serializers.ModelSerializer):
    """
    Serializer for authenticated user information
    Returns user details with groups and permissions for frontend
    """
    groups = serializers.StringRelatedField(many=True, read_only=True)
    permissions = serializers.SerializerMethodField()
    full_name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'email', 'is_staff', 'is_superuser',
            'full_name', 'groups', 'permissions'
        ]

    def get_permissions(self, obj):
        """Get all user permissions for frontend permission checking"""
        return list(obj.get_all_permissions())

    def get_full_name(self, obj):
        """Get user's full name from Customer model if available"""
        try:
            customer = obj.customer
            return f"{customer.first_name} {customer.last_name}".strip()
        except (AttributeError, Customer.DoesNotExist):
            return obj.email.split('@')[0]  # Use email prefix as fallback
