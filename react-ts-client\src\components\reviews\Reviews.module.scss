@import '../../scss/variables';
@import '../../scss/mixins';

.review {
  padding: 5px 5px;
  // background-color: #495200;
  @include flexbox(flex-start, flex-start, column);
  row-gap: 4px;
}

.review__user {
  @include flexbox(flex-start, center);
  column-gap: 5px;
}

// .review__content {
//   @include flexbox(flex-start, flex-start, column);
//   gap: 5px;
// }

.review__content div:nth-child(1) {
  @include flexbox(flex-start, center);
  // gap: 5px;
}

.review__content a {
  margin: 0 5px;
  font-weight: bold;
  color: $primary-dark-text-color;

  &:hover {
    text-decoration: underline;
  }
}

.review__user img {
  border-radius: 50%;
  background-color: #f954ff;
  width: 30px;
  min-height: 30px;
}
