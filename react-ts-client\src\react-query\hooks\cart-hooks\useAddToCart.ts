import { useMutation, useQueryClient } from "@tanstack/react-query"
import { AxiosError } from "axios"
import { ProductShape } from "../../../types/product-types"
import { CartItemShape, ExtraData } from "../../../types/store-types"
import { ErrorResponse } from "../../../types/types"
import cartStore from "../../../zustand/cartStore"
import APIClient from "../../services/api-client"
import { CACHE_KEY_CART_ITEMS } from "../constants"

interface CreateCartShape {
}

const apiClient = new APIClient<CartResponseShape, CreateCartShape>("/cart/")

interface SimpleCartItemShape {
  // cart_id: string
  product_id: number
  // variant_id: number
  quantity: number
  extra_data: ExtraData
  product_variant: number
}

interface CartResponseShape {
  id: string
  cart_items: CartItemShape[]
  customer: string | null
  cart_weight: number
  total_price: number
  shipping_cost: number
  grand_total: number
}

const useAddToCart = (product: ProductShape, qty: number) => {
  const queryClient = useQueryClient()
  const { cartId, setCartId, setCartItemId, selectedVariant, cartItem } = cartStore()
  const { extra_data } = cartItem

  const apiClient2 = new APIClient<CartResponseShape, SimpleCartItemShape>(`/cart/${cartId}/items/`)

  console.log(cartItem)
  console.log(extra_data)

  const addToCartMutation = useMutation({
    mutationFn: ({ product_id, product_variant, quantity, extra_data }: SimpleCartItemShape) =>
      apiClient2.post({
        // cart_id,
        product_id,
        product_variant: product_variant,
        quantity,
        extra_data
      }),
    onSuccess: (data) => {
      setCartItemId(parseInt(data.id))
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEY_CART_ITEMS]
      })
    },
    onError: (error) => {
      console.error('Error adding item to cart:', error)
    }
  })

  const createCartMutation = useMutation<CartResponseShape, AxiosError<ErrorResponse>, CreateCartShape>({
    mutationFn: () => apiClient.post({}),
    onSuccess: (data) => {
      if (!selectedVariant) {
        return
      }
      console.log('cart id (res) in createCartMutation', data)
      setCartId(data.id)
      addToCartMutation.mutate({
        // cart_id: data.id,
        product_id: product.id,
        product_variant: selectedVariant.id,
        quantity: qty,
        extra_data: extra_data
      })
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEY_CART_ITEMS]
      })
    },
    onError: (error) => {
      console.error('Error creating cart:', error)
    }
  })

  const handleAddToCart = () => {

    if (!selectedVariant) {
      return // Early return if no variant selected
    }
    if (cartId) {
      addToCartMutation.mutate({
        // cart_id: cartId,
        product_id: product.id,
        product_variant: selectedVariant.id,
        quantity: qty,
        extra_data: extra_data
      })
    } else {
      createCartMutation.mutate({})
    }
  }

  return {
    handleAddToCart,
    isPending: addToCartMutation.isPending || createCartMutation.isPending,
    error: addToCartMutation.error || createCartMutation.error
  }
}


export default useAddToCart
