from django.db.models import Prefetch
from rest_framework.viewsets import ModelViewSet
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from utils.pagination import DefaultPagination
from .models import Order, OrderItem
from ..customers.models import Customer
from .serializers import OrderSerializer, CreateOrderSerializer
from .permissions import IsCustomerOwner


class OrderViewSet(ModelViewSet):
    """
    Customer-focused order management.
    Customers can view their own orders and create new ones.
    Staff operations are handled by the staff app.
    """
    http_method_names = ['get', 'post', 'head', 'options']  # Removed delete, patch for customers
    permission_classes = [IsCustomerOwner]
    pagination_class = DefaultPagination

    def get_queryset(self):
        """Return only the authenticated customer's orders"""
        if not hasattr(self.request.user, 'customer'):
            return Order.objects.none()

        return Order.objects.filter(
            customer=self.request.user.customer
        ).select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related(
            Prefetch(
                'ordered_items',
                queryset=OrderItem.objects.select_related('product', 'product_variant')
                .prefetch_related(
                    'product_variant__price_label',
                    'product_variant__product_image'
                )
            )
        ).order_by('-placed_at')

    def create(self, request, *args, **kwargs):
        serializer = CreateOrderSerializer(data=request.data, context={'user_id': request.user.id})
        serializer.is_valid(raise_exception=True)
        order = serializer.save()
        return Response(OrderSerializer(order).data, status=status.HTTP_201_CREATED)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CreateOrderSerializer
        return OrderSerializer
