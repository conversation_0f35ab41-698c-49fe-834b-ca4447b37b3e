import { useMutation } from '@tanstack/react-query'
import APIClient from "../../services/auth-client"

interface ActivationDataShape {
  uid: string
  token: string
}

const useAccountActivation = () => {

  const apiClient = new APIClient(`/users/activation/`)

  const mutation = useMutation({
    mutationFn: (activationData: ActivationDataShape) => apiClient.post(activationData),
  })

  return { mutation }
}

export default useAccountActivation