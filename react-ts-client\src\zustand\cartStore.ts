import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { CartStoreShape } from '../types/store-types'

// {
//   title: title,
//   product_image: `https://res.cloudinary.com/dev-kani/${item.product_image[0]?.image}`,
//   quantity: 1,
//   price: item.price,
//   sku: item.sku
// }


const cartStore = create<CartStoreShape>()(
  persist(
    (set) => ({
      cartId: null,
      selectedVariant: null,
      cartItem: {
        id: null,
        product_id: null,
        product_variant: null,
        quantity: null,
        extra_data: {}
      },
      customer: null,
      selectedAddress: null,
      // paymentOptions: [], // unused
      selectedPaymentOption: null,

      setCustomer: (customer) => set({ customer: customer }),
      setSelectedAddress: (address) => set({ selectedAddress: address }),
      // setPaymentOptions: (option) => set({ paymentOptions: option }),
      setSelectedPaymentOption: (paymentOption) => set({ selectedPaymentOption: paymentOption }),

      setCartId: (newCartId) => set({ cartId: newCartId }),
      // setCartItems: (cartItemData) => set({ selectedVariant: cartItemData }),

      setCartItemId: (id: number) => set((state) => ({
        cartItem: { ...state.cartItem, id }
      })),

      // setProductId: (product_id) => set((state) => ({
      //   cartItems: { ...state.cartItems, product_id }
      // })),
      // setProductVariant: (product_variant) => set((state) => ({
      //   cartItems: { ...state.cartItems, product_variant },
      // })),
      setProductVariant: (product_variant) => set({ selectedVariant: product_variant }),
      // setQuantity: (quantity) => set((state) => ({
      //   cartItems: { ...state.cartItems, quantity }
      // })),
      setExtraData: (extra_data) => set((state) => ({
        cartItem: { ...state.cartItem, extra_data }
      })),

      resetExtraData: () => set((state) => ({
        cartItem: { ...state.cartItem, extra_data: {} }
      })),
    }),
    {
      name: 'cart_store',
      // storage: createJSONStorage(() => localStorage), // Persist only the cartId state to localStorage
      // partialize: (state) => ({ cartId: state.cartId }) // Only persist the cartId state
    }
  )
)

export default cartStore

