@import '../../../scss/variables';
@import '../../../scss/mixins';

.new_password {
  padding: $padding-5;
  border-radius: $border-radius-1;
  margin: 2rem 0 0 0;
  width: 400px;
  align-self: flex-start;
  background-color: #fff;
}

// .btn_container {
//   // background-color: rgb(132, 189, 189);
//   display: flex;
//   flex-direction: row;
//   justify-content: center;
//   column-gap: 1rem;
// }