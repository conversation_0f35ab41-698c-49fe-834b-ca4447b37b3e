import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"
import { AddressFormInputs } from '../../../../pages/auth/profile/addresses/ManageAddresses'
import { CUSTOMER_DETAILS } from '../../constants'


interface AddingAddress extends AddressFormInputs {
  customer: number
}

const useCreateAddress = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/customers/addresses/`)

  const createAddress = useMutation({
    mutationFn: (addressData: AddingAddress) => apiClient.post(addressData),

    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { createAddress }
}

export default useCreateAddress