## Product Attribute Filtering Mechanism Workflow

### 1. Initialization
- **On User Navigation**: When the user first navigates to the product listing page (`ProductList` component):
  - The component fetches a list of products based on a search parameters or by category.
  - The `useAttributeFilters` hook is triggered to fetch available filter attributes based on the `productTypeId` from the global state.

### 2. Rendering the Filter UI
- **Filter Display**: The `AttributeFilters` component renders the attribute filtering options on the UI:
  - It receives the available filters (e.g., attribute values like color, size) as props.
  - For each filter attribute value, it generates checkboxes and labels representing the possible values (e.g., "red", "blue" for color).

### 3. User Interaction with Filters
- **User Actions**: When the user interacts with the filters:
  - **Updating the Filter State**:
    - The `handleFilterChange` function is called, passing the filter attribute (e.g., "color") and the selected attribute value (e.g., "red").
    - This function updates the `filterParams` state in the `ProductList` component:
      - If "red" is selected, it’s added to `filterParams` under the "color" key.
      - If "red" is deselected, it’s removed from `filterParams`.

### 4. Filtering Products
- **Upon Filter Update**:
  - **Fetching Filtered Products**:
    - The `useProducts` hook in `ProductList` detects changes in `filterParams`.
    - It makes an API call to fetch products that match the selected filters (e.g., products that are red if "red" is selected).
    - The filtered product list is then rendered on the page.

### 5. Handling Dynamic Changes
- **Dynamic Workflow**:
  - **Product Type Change**:
    - If the product type changes (e.g., from "shirts" to "pants"), the `useAttributeFilters` hook triggers a new API call to fetch filter attributes specific to the new product type.
    - The filters are updated in the UI to reflect the new product type.
  - **No Products Found**:
    - If no products match the selected filters, a message is displayed.
    <!-- - If no filters are applied and no products are found, the filters are reset, and the `productTypeId` is reset to 0. -->

<!-- ### 6. Rerendering the UI
- **UI Updates**:
  - As filters are applied or removed:
    - The `ProductList` component rerenders, showing only the products that match the current filters.
    - The UI remains responsive, reflecting the user’s filter choices in real-time. -->

### 7. Edge Cases & Cleanup
- **Edge Case Handling**:
  - If no products are found for a specific set of filters:
    - The application handles it gracefully by either displaying a "No products found" message or resetting the filters.
- **Cleanup**:
  - When navigating away from the page or when filters are reset:
    - Any temporary state (like `filterParams`) is cleaned up to avoid memory leaks or stale data.
