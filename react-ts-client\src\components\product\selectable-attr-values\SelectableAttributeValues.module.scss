@import '../../../scss/mixins';

.attribute_value_group {
  h3 {
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: #333;
  }

  ul {
    @include flexbox(flex-start, flex-start, row);

    li {
      @include flexbox(flex-start, center);

      label {
        margin: 0 0 0 2px;
      }
    }
  }
}

.selected_value_display {
  font-weight: 700;
  color: #0091cf;
  margin-left: 0.5rem;
  padding: 0.125rem 0.5rem;
  background-color: rgba(0, 145, 207, 0.1);
  border-radius: 3px;
  font-size: 16px;
  text-transform: none;
  border: 1px solid rgba(0, 145, 207, 0.2);
}