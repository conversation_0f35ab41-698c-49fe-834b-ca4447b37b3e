ERROR Staff API Exception: Object of type Brand is not JSON serializable for POST /api/staff/products/products/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 86, in perform_create
    AuditService.log_product_action(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        product=product,
        ^^^^^^^^^^^^^^^^
    ...<3 lines>...
        request=self.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\services.py", line 297, in log_product_action
    return ProductAudit.objects.create(**audit_data)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\postgresql\psycopg_any.py", line 113, in getquoted
    quoted = super().getquoted()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 78, in getquoted
    s = self.dumps(self.adapted)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 72, in dumps
    return self._dumps(obj)
           ~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Brand is not JSON serializable
Internal Server Error: /api/staff/products/products/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 86, in perform_create
    AuditService.log_product_action(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        product=product,
        ^^^^^^^^^^^^^^^^
    ...<3 lines>...
        request=self.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\services.py", line 297, in log_product_action
    return ProductAudit.objects.create(**audit_data)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\postgresql\psycopg_any.py", line 113, in getquoted
    quoted = super().getquoted()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 78, in getquoted
    s = self.dumps(self.adapted)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 72, in dumps
    return self._dumps(obj)
           ~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Brand is not JSON serializable
ERROR Internal Server Error: /api/staff/products/products/
Traceback (most recent call last):
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\rest_framework\mixins.py", line 19, in create
    self.perform_create(serializer)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\views.py", line 86, in perform_create
    AuditService.log_product_action(
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        product=product,
        ^^^^^^^^^^^^^^^^
    ...<3 lines>...
        request=self.request
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "D:\Projects\Projects Repos\picky-ecommerce-app\picky-store\server\apps\staff\products\services.py", line 297, in log_product_action
    return ProductAudit.objects.create(**audit_data)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 663, in create
    obj.save(force_insert=True, using=self.db)
    ~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 902, in save
    self.save_base(
    ~~~~~~~~~~~~~~^
        using=using,
        ^^^^^^^^^^^^
    ...<2 lines>...
        update_fields=update_fields,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1008, in save_base
    updated = self._save_table(
        raw,
    ...<4 lines>...
        update_fields,
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1169, in _save_table
    results = self._do_insert(
        cls._base_manager, using, fields, returning_fields, raw
    )
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\base.py", line 1210, in _do_insert
    return manager._insert(
           ~~~~~~~~~~~~~~~^
        [self],
        ^^^^^^^
    ...<3 lines>...
        raw=raw,
        ^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\query.py", line 1868, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\models\sql\compiler.py", line 1882, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 235, in execute
    return self._record(super().execute, sql, params)
           ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\debug_toolbar\panels\sql\tracking.py", line 160, in _record
    return method(sql, params)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 122, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 79, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 92, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\utils.py", line 105, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\django\db\backends\postgresql\psycopg_any.py", line 113, in getquoted
    quoted = super().getquoted()
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 78, in getquoted
    s = self.dumps(self.adapted)
  File "C:\Users\<USER>\.virtualenvs\server-Z5gLBdpX\Lib\site-packages\psycopg2\_json.py", line 72, in dumps
    return self._dumps(obj)
           ~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\__init__.py", line 231, in dumps
    return _default_encoder.encode(obj)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 200, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 261, in iterencode
    return _iterencode(o, 0)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\json\encoder.py", line 180, in default
    raise TypeError(f'Object of type {o.__class__.__name__} '
                    f'is not JSON serializable')
TypeError: Object of type Brand is not JSON serializable