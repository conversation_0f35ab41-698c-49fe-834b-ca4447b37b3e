// Base tooltip styles
.tooltip {
  position: relative;
  cursor: pointer;
}

// Tooltip content (the actual tooltip bubble)
.tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  background-color: #333;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  visibility: hidden;
  white-space: nowrap;
  font-size: 12px;
  pointer-events: none;
  transition: opacity 0.2s ease, visibility 0.3s ease;
  z-index: 1000;
}

// Position variants
.tooltip--top::after {
  bottom: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.tooltip--bottom::after {
  top: calc(100% + 6px);
  left: 50%;
  transform: translateX(-50%);
}

.tooltip--left::after {
  right: calc(100% + 6px);
  top: 50%;
  transform: translateY(-50%);
}

.tooltip--right::after {
  left: calc(100% + 6px);
  top: 50%;
  transform: translateY(-50%);
}

// Show tooltip on hover (default behavior)
.tooltip--hover:hover::after {
  opacity: 1;
  visibility: visible;
}

// Show tooltip based on condition (for disabled buttons, etc.)
.tooltip--condition::after {
  opacity: 1;
  visibility: visible;
}

// Arrow indicators for better visual connection
.tooltip::before {
  content: '';
  position: absolute;
  border: 4px solid transparent;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.3s ease;
  z-index: 1001;
}

// Arrow positions
.tooltip--top::before {
  bottom: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
  border-top-color: #333;
}

.tooltip--bottom::before {
  top: calc(100% + 2px);
  left: 50%;
  transform: translateX(-50%);
  border-bottom-color: #333;
}

.tooltip--left::before {
  right: calc(100% + 2px);
  top: 50%;
  transform: translateY(-50%);
  border-left-color: #333;
}

.tooltip--right::before {
  left: calc(100% + 2px);
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #333;
}

// Show arrows on hover
.tooltip--hover:hover::before {
  opacity: 1;
  visibility: visible;
}

// Show arrows based on condition
.tooltip--condition::before {
  opacity: 1;
  visibility: visible;
}

// Responsive adjustments
@media (max-width: 768px) {
  .tooltip::after {
    font-size: 11px;
    padding: 3px 6px;
    max-width: 200px;
    white-space: normal;
    word-wrap: break-word;
  }
}