from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group, Permission
from django.core.cache import cache
from .models import GroupMembership, PermissionAudit, APIAccessLog
import logging

User = get_user_model()
logger = logging.getLogger(__name__)


class GroupService:
    """
    Service class for group-related operations
    Handles group creation, membership management, and permission assignment
    """

    def create_group(self, name, permissions=None, created_by=None):
        """
        Create a new group with optional permissions
        """
        group, created = Group.objects.get_or_create(name=name)

        if created and permissions:
            # Add permissions to the group
            permission_objects = Permission.objects.filter(codename__in=permissions)
            group.permissions.set(permission_objects)

        # Log the action
        AuditService.log_action(
            action='group_created',
            performed_by=created_by,
            target_group=group,
            details={'permissions': permissions or []}
        )

        return group, created

    def update_group(self, group, validated_data, updated_by=None, ip_address=None):
        """
        Update a group with audit logging
        """
        old_permissions = set(group.permissions.values_list('codename', flat=True))

        # Update group fields
        for attr, value in validated_data.items():
            if attr != 'permission_ids':
                setattr(group, attr, value)
        group.save()

        # Update permissions if provided
        permission_ids = validated_data.get('permission_ids')
        if permission_ids is not None:
            permissions = Permission.objects.filter(id__in=permission_ids)
            group.permissions.set(permissions)

        new_permissions = set(group.permissions.values_list('codename', flat=True))

        # Log the action
        AuditService.log_action(
            action='group_updated',
            performed_by=updated_by,
            target_group=group,
            details={
                'old_permissions': list(old_permissions),
                'new_permissions': list(new_permissions)
            },
            ip_address=ip_address
        )

        return group

    def delete_group(self, group, deleted_by=None, ip_address=None):
        """
        Delete a group with audit logging
        """
        group_name = group.name

        # Log the action before deletion
        AuditService.log_action(
            action='group_deleted',
            performed_by=deleted_by,
            target_group=group,
            details={'group_name': group_name},
            ip_address=ip_address
        )

        group.delete()

    def add_user_to_group(self, user, group, assigned_by=None, notes="", ip_address=None):
        """
        Add a user to a group with audit logging
        """
        # Check if membership already exists
        membership, created = GroupMembership.objects.get_or_create(
            user=user,
            group=group,
            defaults={
                'assigned_by': assigned_by,
                'notes': notes,
                'is_active': True
            }
        )

        if not created:
            # Reactivate if it was inactive
            if not membership.is_active:
                membership.is_active = True
                membership.assigned_by = assigned_by
                membership.notes = notes
                membership.save()
                created = True

        if created:
            # Add user to Django's built-in group system
            user.groups.add(group)

            # Log the action
            AuditService.log_action(
                action='user_added_to_group',
                performed_by=assigned_by,
                target_user=user,
                target_group=group,
                details={'notes': notes},
                ip_address=ip_address
            )

        return membership, created

    def remove_user_from_group(self, user, group, removed_by=None, ip_address=None):
        """
        Remove a user from a group with audit logging
        """
        try:
            membership = GroupMembership.objects.get(user=user, group=group, is_active=True)
            membership.is_active = False
            membership.save()

            # Remove from Django's built-in group system
            user.groups.remove(group)

            # Log the action
            AuditService.log_action(
                action='user_removed_from_group',
                performed_by=removed_by,
                target_user=user,
                target_group=group,
                ip_address=ip_address
            )

            return True
        except GroupMembership.DoesNotExist:
            return False

    def bulk_assign_users(self, user_ids, group, assigned_by=None, notes="", ip_address=None):
        """
        Assign multiple users to a group
        """
        users = User.objects.filter(id__in=user_ids, is_active=True)
        results = []

        for user in users:
            membership, created = self.add_user_to_group(
                user=user,
                group=group,
                assigned_by=assigned_by,
                notes=notes,
                ip_address=ip_address
            )
            results.append({
                'user': user,
                'membership': membership,
                'created': created
            })

        return results

    def get_group_members(self, group):
        """
        Get all active members of a group
        """
        return GroupMembership.objects.filter(
            group=group,
            is_active=True
        ).select_related('user', 'assigned_by')


class AuditService:
    """
    Service class for audit logging
    Handles all audit trail operations
    """

    @staticmethod
    def log_action(action, performed_by=None, target_user=None, target_group=None,
                   details=None, ip_address=None):
        """
        Log an action to the audit trail
        """
        try:
            PermissionAudit.objects.create(
                action=action,
                performed_by=performed_by,
                target_user=target_user,
                target_group=target_group,
                details=details or {},
                ip_address=ip_address
            )
            logger.info(f"Audit log created: {action} by {performed_by}")
        except Exception as e:
            logger.error(f"Failed to create audit log: {e}")

    @staticmethod
    def get_audit_logs(filters=None):
        """
        Get audit logs with optional filtering
        """
        queryset = PermissionAudit.objects.all().select_related(
            'performed_by', 'target_user', 'target_group'
        )

        if filters:
            if 'action' in filters:
                queryset = queryset.filter(action=filters['action'])
            if 'user_id' in filters:
                queryset = queryset.filter(performed_by_id=filters['user_id'])
            if 'from_date' in filters:
                queryset = queryset.filter(timestamp__gte=filters['from_date'])
            if 'to_date' in filters:
                queryset = queryset.filter(timestamp__lte=filters['to_date'])

        return queryset

    @staticmethod
    def log_api_access(user, endpoint, method, status_code, ip_address,
                       user_agent, response_time):
        """
        Log API access for monitoring
        """
        try:
            APIAccessLog.objects.create(
                user=user,
                endpoint=endpoint,
                method=method,
                status_code=status_code,
                ip_address=ip_address,
                user_agent=user_agent,
                response_time=response_time
            )
        except Exception as e:
            logger.error(f"Failed to log API access: {e}")


class SecurityService:
    """
    Service class for security-related operations
    """

    @staticmethod
    def get_client_ip(request):
        """
        Get the client IP address from the request
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '127.0.0.1')
        return ip

    @staticmethod
    def get_user_agent(request):
        """
        Get the user agent from the request
        """
        return request.META.get('HTTP_USER_AGENT', '')

    def validate_staff_user(self, user):
        """
        Validate that the user is eligible for staff operations
        """
        return (
                user and
                user.is_authenticated and
                user.is_active and
                user.is_staff
        )


class PermissionService:
    """
    Service class for permission-related operations
    """

    def get_user_groups(self, user):
        """
        Get all groups for a user with caching
        """
        cache_key = f'staff_user_groups_{user.id}'
        groups = cache.get(cache_key)

        if groups is None:
            groups = list(user.groups.all().values_list('name', flat=True))
            cache.set(cache_key, groups, 300)  # Cache for 5 minutes

        return groups

    def get_user_all_permissions(self, user):
        """
        Get all permissions for a user (from groups and direct assignments)
        """
        cache_key = f'staff_user_permissions_{user.id}'
        permissions = cache.get(cache_key)

        if permissions is None:
            permissions = list(user.get_all_permissions())
            cache.set(cache_key, permissions, 300)  # Cache for 5 minutes

        return permissions

    def clear_user_cache(self, user):
        """
        Clear all cached permissions for a user
        """
        cache.delete(f'staff_user_groups_{user.id}')
        cache.delete(f'staff_user_permissions_{user.id}')

    def check_permission(self, user, permission):
        """
        Check if a user has a specific permission
        """
        return user.has_perm(permission)

    def get_grouped_permissions(self):
        """
        Get all permissions grouped by content type
        """
        permissions = Permission.objects.all().select_related('content_type')
        grouped = {}

        for permission in permissions:
            app_label = permission.content_type.app_label
            model = permission.content_type.model
            key = f"{app_label}.{model}"

            if key not in grouped:
                grouped[key] = {
                    'app_label': app_label,
                    'model': model,
                    'model_name': permission.content_type.name,
                    'permissions': []
                }

            grouped[key]['permissions'].append({
                'id': permission.id,
                'name': permission.name,
                'codename': permission.codename
            })

        return grouped

    @staticmethod
    def log_staff_creation(staff_user, staff_profile, performed_by=None, ip_address=None):
        """
        Log staff user creation with profile details
        """
        AuditService.log_action(
            action='staff_user_created',
            performed_by=performed_by,
            target_user=staff_user,
            details={
                'employee_id': staff_profile.employee_id,
                'department': staff_profile.department,
                'position_title': staff_profile.position_title,
                'hire_date': staff_profile.hire_date.isoformat(),
                'manager_id': staff_profile.manager.id if staff_profile.manager else None
            },
            ip_address=ip_address
        )

    @staticmethod
    def log_staff_status_change(staff_profile, old_status, new_status, performed_by=None, ip_address=None):
        """
        Log staff status changes
        """
        AuditService.log_action(
            action='staff_status_changed',
            performed_by=performed_by,
            target_user=staff_profile.user,
            details={
                'employee_id': staff_profile.employee_id,
                'old_status': old_status,
                'new_status': new_status,
                'department': staff_profile.department,
                'position_title': staff_profile.position_title
            },
            ip_address=ip_address
        )

    @staticmethod
    def log_role_permission_change(role, action_type, permission_codename, performed_by=None, ip_address=None):
        """
        Log role permission additions/removals
        """
        AuditService.log_action(
            action=action_type,  # 'permission_added_to_role' or 'permission_removed_from_role'
            performed_by=performed_by,
            target_group=role,
            details={
                'role_name': role.name,
                'permission_codename': permission_codename,
                'total_permissions': role.permission_count
            },
            ip_address=ip_address
        )

    @staticmethod
    def log_security_event(event_type, user=None, details=None, ip_address=None):
        """
        Log security-related events
        """
        AuditService.log_action(
            action=event_type,
            performed_by=user,
            target_user=user,
            details=details or {},
            ip_address=ip_address
        )
