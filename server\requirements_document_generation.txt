# Document Generation Dependencies
# Add these to your main requirements.txt or install separately

# PDF Generation
reportlab>=4.0.0          # Primary PDF generation library
Pillow>=10.0.0            # Image processing for PDFs

# Alternative PDF libraries (optional)
# weasyprint>=60.0         # HTML to PDF conversion
# xhtml2pdf>=0.2.5         # Another HTML to PDF option

# Printing support (optional)
# cups-python>=1.0.0       # CUPS printing support for Linux/macOS
# win32print               # Windows printing support (Windows only)

# File handling
python-magic>=0.4.27      # File type detection
