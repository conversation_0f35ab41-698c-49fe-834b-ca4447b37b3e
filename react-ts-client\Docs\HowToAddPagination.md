# How to add Pagination to a component

## Overview

This guide explains the standard approach for implementing pagination in components across our project.

## Step 1

- Make sure pagination class is added on Django server's views.py

## Step 2 (Hook Setup)

- Create or modify the data fetching hook to accept page parameter
- Include page number in queryKey for proper cache management
- Use APIClient's getAll method which handles paginated responses

```typescript
const useData = (page: number) => {
  const apiClient = new APIClient<DataShape>('/endpoint/')
  
  return useQuery({
    queryKey: [CACHE_KEY, page],
    queryFn: () => apiClient.getAll({
      params: { page }
    })
  })
}
```

## Step 3 (Component Configuration)

- Import useSearchParams for URL-based pagination state
- Set up page parameter from URL with a default value
- Implement page change handler

```typescript
const [searchParams, setSearchParams] = useSearchParams()
const page = parseInt(searchParams.get("page") || "1", 10)

const handlePageChange = (newPage: number) => {
  setSearchParams({ page: newPage.toString() })
}
```

## Step 4 (Pagination UI Integration)

- Import the Pagination component
- Calculate total pages using data.count and ITEMS_PER_PAGE
- Add Pagination component to TSX

```typescript
{data && data.count > 0 && (
  <Pagination
    currentPage={page}
    totalPages={Math.ceil(data.count / ITEMS_PER_PAGE)}
    onPageChange={handlePageChange}
  />
)}
```

## Expected API Response Structure

```json
{
  count: number;        // Total items count
  results: T[];         // Array of items for current page
  next: string | null;  // URL for next page
  previous: string | null; // URL for previous page
}
```

## Reference Components

- See OrderList.tsx, Wishlist.tsx for a complete implementation example
- Check useGetAllOrders.ts for hook implementation pattern
