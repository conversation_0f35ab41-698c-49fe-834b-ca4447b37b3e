import { useMutation } from '@tanstack/react-query'
import APIClient from '../../../services/auth-client'
import { VerifyContactShape } from '../../../../pages/auth/register/5-update-auth-info/VerifyAuthContact'


const useSendVerifyCode = () => {
  const apiClient = new APIClient('add-contact/verify/')

  const mutation = useMutation({
    mutationFn: (data: VerifyContactShape) => apiClient.post(data)
  })

  return { mutation }
}

export default useSendVerifyCode
