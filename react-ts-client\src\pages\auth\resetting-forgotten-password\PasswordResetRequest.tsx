import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { AxiosError } from 'axios'
import Alert from '../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../components/utils/getErrorMessage'
import { ErrorResponse } from '../../../types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { resetPasswordInitSchema } from '../../../types/schemas'
import { z } from 'zod'
import Logo from '../../../components/utils/logo/Logo'
import loading_svg from '../../../assets/loading_svg_white.svg'
import useInitResetPassword from '../../../react-query/hooks/auth-hooks/password-changes/reset-forgotten-password/useResetPassword'
import styles from './PasswordReset.module.scss'


export type ResetRequestShape = z.infer<typeof resetPasswordInitSchema>

interface Props {
  onRequestSuccess: (email_or_phone: ResetRequestShape) => void
}

const PasswordResetRequest = ({ onRequestSuccess }: Props) => {
  const { mutation } = useInitResetPassword()
  const { register, handleSubmit, formState: { errors } } = useForm<ResetRequestShape>({
    resolver: zodResolver(resetPasswordInitSchema)
  })

  const onSubmit: SubmitHandler<ResetRequestShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: (res) => {
        onRequestSuccess({ email_or_phone: res.email_or_phone })
      }
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
          />
        )}
        {!mutation.error && (
          <Alert
            variant="info"
            message="Enter your user account's verified email address or 
            phone number and we will send you a verification code."
          />
        )}
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <div className='form_group'>
            <label className='form_label'>Enter your registered email or phone:</label>
            <input
              disabled={mutation.isPending}
              type="text"
              placeholder='Eg: +***********'
              className='form_input'
              {...register("email_or_phone")}
            />
            {errors.email_or_phone && <p className='form_error'>{errors.email_or_phone.message}</p>}
          </div>
          <div className={styles.btn_container_2}>
            <button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_svg} alt="Loading..." className='loading_svg' />
              ) : 'Submit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default PasswordResetRequest
