@import '../../../../scss/variables';
@import '../../../../scss/mixins';
@import '../../../../scss/animations.scss';


.register_container {
  background-image: url(../../../../assets/images/pc_hardware.jpg);
  background-color: #70829e;
  background-size: cover;
  background-repeat: no-repeat;
  height: 100vh;
  position: relative;
  overflow: hidden; // Add this to prevent the blur from extending outside

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: inherit;
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    z-index: 0; // Change this to 0
  }

  // Add this new rule
  >* {
    position: relative;
    z-index: 1;
  }
}

.form_container {
  width: 400px;
  box-shadow: #0000000d 0px 0px 0px 1px, #d1d5db 0px 0px 0px 1px inset;
  padding: $padding-5;
  background-color: #fff;
  border-radius: $border-radius-1;
  margin: 1rem auto 0 auto;
  @include slideInAnimation();

  button {
    margin: 1.5rem auto 1rem auto;
    @include btn(#fff, $primary-blue);
    width: 100%;
    padding: 8px 0;
    transition: all 0.3s ease-in;

    &:hover {
      background-color: darken($primary-blue, 10%);
    }
  }

  .form {
    .email_input {
      @include flexbox(flex-start, flex-start, column);
      margin: 15px 0;
      row-gap: 4px;

      label {
        font-weight: bold;
        color: $primary-dark-blue;
      }

      input {
        border: .1px solid $primary-dark-text-color;
        border-radius: 3px;
        padding: 5px 5px;
        font-size: 16.5px;

        &:focus {
          outline: 2px solid $lighten-blue;
          border: none;
        }
      }

      p {
        color: $error-red;
        text-align: center
      }
    }
  }
}

.btn_container_2 {
  // background-color: rgb(132, 189, 189);
  @include flexbox(center, flex-start, column);
  row-gap: 1rem;

  // margin: 0;
  button {
    margin: 0;
  }

  .empty_btn {
    @include btn($lighten-blue, #fff);
    // margin: 0 auto 0 auto;
    padding: .36rem 1.2rem;
    border: 1px solid $lighten-blue;
    letter-spacing: .7px;
    transition: all 0.2s ease;
    // font-size: 4rem;

    &:hover {
      border: 1px solid $primary-blue;
      background-color: #fff;
      color: $primary-blue;
    }
  }
}

.login_or_register {
  text-align: center;

  a {
    color: $primary-blue;
    transition: all 0.3s ease-in;

    &:hover {
      color: darken($primary-blue, 10%);
      text-decoration: underline;
    }
  }
}

@media (width > $tablet) {
  .register_container {
    padding-top: 2rem;
  }

  .form_container {
    padding: 25px;
  }
}