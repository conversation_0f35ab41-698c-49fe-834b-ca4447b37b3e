import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import loading_svg from '../../../assets/loading_svg_white.svg'
// import useResetPasswordConfirm from '../../../react-query/hooks/auth-hooks/password-changes/useResetPasswordConfirm'
import Alert from '../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../types/types'
import { zodResolver } from '@hookform/resolvers/zod'
import Logo from '../../../components/utils/logo/Logo'
import { z } from 'zod'
import { restPasswordVerifySchema } from '../../../types/schemas'
import { FaEye, FaEyeSlash } from 'react-icons/fa'

import styles from './PasswordReset.module.scss'
import useTogglePasswordVisibility from '../../../hooks/useTogglePasswordVisibility'
import useResetPasswordConfirm from '../../../react-query/hooks/auth-hooks/password-changes/reset-forgotten-password/useResetPasswordConfirm'


export type VerifyDetailShape = z.infer<typeof restPasswordVerifySchema>

interface Props {
  emailOrPhone: string
  onVerificationSuccess: () => void
}

const VerifyResetCode = ({ emailOrPhone, onVerificationSuccess }: Props) => {

  const { isVisible: isPasswordVisible, toggleVisibility: togglePasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isRePasswordVisible, toggleVisibility: toggleRePasswordVisibility } = useTogglePasswordVisibility()

  const { mutation } = useResetPasswordConfirm()
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyDetailShape>({
    resolver: zodResolver(restPasswordVerifySchema)
  })

  const onSubmit: SubmitHandler<VerifyDetailShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: onVerificationSuccess,
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
          // textAlign='center'
          />
        )}
        {!mutation.error && (
          <Alert
            variant="success"
            message={`A Verification Code has been sent to ${emailOrPhone}. 
          Please check your ${emailOrPhone?.startsWith('+') ? 'phone' : 'email'} and enter the code below.`}
            highlightWords={[`${emailOrPhone}`, "phone", "email"]}
            textAlign='center'
          />
        )}
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          {mutation.error && (
            <Alert
              variant="error"
              message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
              textAlign='center'
            />
          )}
          <input type="hidden" {...register("email_or_phone")} value={emailOrPhone} />

          <div className='form_group'>
            <label className='form_label'>Enter Verification Code:</label>
            <input className='form_input' type="number" {...register("code", { valueAsNumber: true })} />
            {errors.code && <p className='form_error'>{errors.code.message}</p>}
          </div>

          <div className='form_group'>
            <label className='form_label'>New Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                type={isPasswordVisible ? "text" : "password"} {...register("new_password")} />
              <span onClick={togglePasswordVisibility}>
                <i>{isPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.new_password && <p className='form_error'>{errors.new_password.message}</p>}
          </div>

          <div className='form_group'>
            <label className='form_label'>Confirm Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                type={isRePasswordVisible ? "text" : "password"}
                {...register("confirm_password")}
              />
              <span onClick={toggleRePasswordVisibility}>
                <i>{isRePasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.confirm_password && <p className='form_error'>{errors.confirm_password.message}</p>}
          </div>

          <div className={styles.btn_container_2}>
            <button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_svg} alt="Loading..." className='loading_svg' />
              ) : 'Submit'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default VerifyResetCode
