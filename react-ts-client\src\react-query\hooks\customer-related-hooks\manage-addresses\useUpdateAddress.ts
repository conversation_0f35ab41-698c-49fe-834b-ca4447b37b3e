import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"
import { CUSTOMER_DETAILS } from '../../constants'


// All values are optional because only changed values will be sent to the server. 
export interface PartialAddressInputs {
  id?: number,
  full_name?: string,
  street_name?: string,
  address_line_1?: string,
  address_line_2?: string,
  postal_code?: string,
  city_or_village?: string,
}

const useUpdateAddress = (addressId: number) => {
  const queryClient = useQueryClient()
  console.log(addressId)

  const apiClient = new APIClient(`/customers/addresses/${addressId}/`)

  const updateAddress = useMutation({
    mutationFn: (addressDetails: PartialAddressInputs) => apiClient.patch(addressDetails),

    onSuccess: () => {
      queryClient.invalidateQueries({
        // Invalidate customer details because initial address data is from customer details
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { updateAddress }
}

export default useUpdateAddress