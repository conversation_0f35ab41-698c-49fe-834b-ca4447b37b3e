import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../services/auth-client"
import { passwordSchemaFormInputs } from '../../../../pages/auth/register/3-set-password/SetPassword'


const useSetPassword = () => {

  const apiClient = new APIClient(`/set-password/`)

  const mutation = useMutation({
    mutationFn: (data: passwordSchemaFormInputs) => apiClient.post(data)
  })

  return { mutation }
}

export default useSetPassword