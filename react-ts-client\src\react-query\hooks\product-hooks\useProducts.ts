import { useQuery } from '@tanstack/react-query'
import APIClient from '../../services/api-client'
import filterStore from '../../../zustand/filterStore'
import { CACHE_KEY_PRODUCTS } from '../constants'
import { ProductShape } from '../../../types/product-types'

const useProducts = (slug: string, page: number, queryString: string, searchQuery: string = '') => {
  const { selectedFilters } = filterStore()

  // Initialize API client based on whether searchQuery exists or not
  const apiClient = searchQuery
    ? new APIClient<ProductShape>(`/products/?search=${searchQuery}`)
    : new APIClient<ProductShape>(`/products/category/${slug}/`)

  const queryParams = new URLSearchParams(queryString)

  // Add filters to the query parameters
  Object.entries(selectedFilters).forEach(([key, value]) => {
    if (key === 'price_range' && Array.isArray(value)) {
      queryParams.set('min_price', value[0].toString())
      queryParams.set('max_price', value[1].toString())
    } else if (Array.isArray(value)) {
      value.forEach((val) => queryParams.append(key, val.toString()))
    } else {
      queryParams.set(key, value.toString())
    }
  })

  // If searchQuery exists, add it to queryParams
  if (searchQuery) {
    queryParams.set('search', searchQuery)
  }

  return useQuery({
    queryKey: [CACHE_KEY_PRODUCTS, slug, selectedFilters, page, queryString, searchQuery],
    queryFn: () => apiClient.getAll({ params: Object.fromEntries(queryParams) }),
  })
}

export default useProducts
