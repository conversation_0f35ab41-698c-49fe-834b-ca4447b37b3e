import { useEffect, useState } from 'react'
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { useNavigate, Link } from 'react-router-dom'
import { zodResolver } from '@hookform/resolvers/zod'
import { AxiosError } from 'axios'
import useRegister from '../../../../react-query/hooks/auth-hooks/registration/useRegister'
import Alert from '../../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { ErrorResponse } from '../../../../types/types'
import PhoneNumberInput from '../../../../components/utils/phone-number-input/PhoneNumberInput'
import Logo from '../../../../components/utils/logo/Logo'
import loading_svg from '../../../../assets/loading_svg_white.svg'
import authStore from '../../../../zustand/authStore'
import { registerSchema } from '../../../../types/schemas'
import styles from './InitiateRegistration.module.scss'

type RegisterFormInputs = {
  username: string
}

export interface InitRegUserShape {
  email?: string
  phone_number?: string
}

const InitiateRegistration = () => {
  const navigate = useNavigate()
  const { mutation } = useRegister()
  const { isLoggedIn, setRegInitiated } = authStore()
  const [isPhoneInput, setIsPhoneInput] = useState(false)
  const [usernameValue, setUsernameValue] = useState('')

  const { register, handleSubmit, formState: { errors }, setValue } = useForm<RegisterFormInputs>({
    resolver: zodResolver(registerSchema)
  })

  // Handle input change and sync with form state
  const handleInputChange = (value: string) => {
    setUsernameValue(value)
    setValue("username", value) // Sync with react-hook-form
  }
  // Watch for changes to usernameValue and toggle phone input mode
  useEffect(() => {
    if (usernameValue && /^[0-9+]/.test(usernameValue)) {
      setIsPhoneInput(true)
    } else {
      setIsPhoneInput(false)
    }
  }, [usernameValue])

  // Navigate on successful registration
  useEffect(() => {
    if (mutation.isSuccess) {
      setRegInitiated(true)
      navigate('/user/verify-reg-credentials')
    }
  }, [mutation.isSuccess, navigate, setRegInitiated])

  useEffect(() => {
    if (isLoggedIn) {
      navigate('/customer')
    }
  }, [isLoggedIn, navigate])

  console.log('InitiateRegistration:', isLoggedIn)

  const onSubmit: SubmitHandler<RegisterFormInputs> = async (data) => {
    let submissionData: InitRegUserShape = {}

    if (isPhoneInput) {
      const username = data.username.startsWith('+') ? data.username : `+${data.username}`
      submissionData = { phone_number: username }
    } else {
      submissionData = { email: data.username }
    }

    console.log(submissionData)
    mutation.mutate(submissionData)
  }

  return (
    <div className={styles.register_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        <>
          {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
          <h2 className='title'>Create your account</h2>
          <form onSubmit={handleSubmit(onSubmit)} className='form'>
            <div className='form_group'>
              <label className='form_label' htmlFor="username">Email or Phone number:</label>
              {isPhoneInput ? (
                <PhoneNumberInput
                  defaultCountry="lk"
                  onlyCountries={['lk', 'us']}
                  pending={mutation.isPending}
                  value={usernameValue || ''}
                  onChange={(value: string) => handleInputChange(value)}
                  placeholder='Enter phone number'
                />
              ) : (
                <div className={styles.form_group}>
                  <input
                    className='form_input'
                    type="email"
                    id="username"
                    disabled={mutation.isPending}
                    {...register("username")}
                    value={usernameValue}
                    onChange={(e) => handleInputChange(e.target.value)}
                    placeholder="Enter email or phone number"
                  />
                </div>
              )}
              {errors.username && <p className='form_error'>{errors.username.message} &#128543; Eg: +***********</p>}
            </div>

            {/* Register with an email */}
            {/* <div className='form_group'>
              <label className='form_label' htmlFor="username">Enter a Phone number:</label>
              <input
                className='form_input'
                type="email"
                id="username"
                disabled={mutation.isPending}
                {...register("username")}
                value={usernameValue}
                onChange={(e) => handleInputChange(e.target.value)}
                placeholder="Enter email or phone number"
              />
            </div> */}

            {/* Register with an phone number   */}
            {/* <div className='form_group'>
              <label className='form_label' htmlFor="username">Enter a Phone number:</label>
              <PhoneNumberInput
                defaultCountry="lk"
                onlyCountries={['lk', 'us']}
                pending={mutation.isPending}
                value={usernameValue || ''}
                onChange={(value: string) => handleInputChange(value)}
                placeholder='Enter phone number'
              />
              {errors.username && <p className='form_error'>{errors.username.message} &#128543; Eg: +***********</p>}
            </div> */}

            <button type="submit" disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_svg} alt="Loading..." className='loading_svg' />
              ) : (
                'Continue'
              )}
            </button>
            <p className={styles.login_or_register}>
              Already have an account?
              <Link to='/user/login'> Login</Link>
            </p>
          </form>
        </>
      </div>
    </div>
  )
}

export default InitiateRegistration
