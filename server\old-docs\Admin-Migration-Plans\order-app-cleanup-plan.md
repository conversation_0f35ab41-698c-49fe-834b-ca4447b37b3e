# Order App Cleanup Plan - RBAC System Migration

## Overview
This document outlines the plan to clean up the order app by removing outdated role-based code (`OrderVerifiers`, `LogisticsCoordinators`) and aligning it with the new RBAC system. The order app should be primarily customer-focused, with staff operations moved to the dedicated staff app.

## Current Issues

### 1. Outdated Role References
- **File**: `apps/order/views.py` (lines 38-41)
- **Issue**: Hardcoded group names `'OrderVerifiers'` and `'LogisticsCoordinators'`
- **Impact**: These groups don't exist in the new RBAC system

### 2. Mixed Permission Logic
- **File**: `utils/permissions.py` (lines 56-68)
- **Issue**: `IsAdminOrGroupMember` class contains outdated group references
- **Impact**: Permission checks fail for staff users

### 3. Inconsistent API Design
- **Issue**: Order app serves both customers and staff with different permission logic
- **Impact**: Violates separation of concerns principle

## New RBAC Groups for Orders

| Group Name | Code | Permissions | Purpose |
|------------|------|-------------|---------|
| Order Management Executive | `OME` | Full order CRUD + customer view/change | Complete order management |
| Order Management Group Member | `OMGM` | Order change/view + customer view | Limited order operations |
| Order Fulfillment Specialist | `OFS` | Order view/change (fulfillment focus) | Order processing and fulfillment |

## Cleanup Steps

### Phase 1: Remove Outdated Code

#### 1.1 Update Order Views
**File**: `apps/order/views.py`

**Current problematic code (lines 35-42):**
```python
user_groups = set(user.groups.values_list('name', flat=True))
if user.is_staff or user.is_superuser:
    return base_queryset.distinct()
if 'OrderVerifiers' in user_groups:
    return base_queryset.filter(delivery_status='Pending').distinct()
if 'LogisticsCoordinators' in user_groups:
    return base_queryset.filter(delivery_status='Processing').distinct()
return base_queryset.filter(customer__user=user).order_by('-placed_at').distinct()
```

**Replacement strategy:**
- Remove staff-specific filtering logic
- Focus on customer-only access
- Staff operations will be handled by staff app

#### 1.2 Clean Up Permission Classes
**File**: `utils/permissions.py`

**Current problematic code (lines 56-68):**
```python
user_groups = request.user.groups.filter(name__in=['LogisticsCoordinators', 'OrderVerifiers']).values_list('name', flat=True)
if 'OrderVerifiers' in user_groups and request.data.get('delivery_status') == 'Processing':
    return True
if 'LogisticsCoordinators' in user_groups and request.data.get('delivery_status') == 'Dispatched':
    return True
```

**Replacement strategy:**
- Remove hardcoded group references
- Simplify to customer-focused permissions
- Create new staff-specific permission classes

### Phase 2: Implement Customer-Focused Order API

#### 2.1 New Permission Classes
Create customer-focused permission classes:

```python
class IsCustomerOrStaff(BasePermission):
    """Allow customers to access their own orders, staff to access all"""
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        if request.user.is_staff:
            return True
        return hasattr(request.user, 'customer') and obj.customer == request.user.customer

class IsCustomerOwner(BasePermission):
    """Allow only customers to access their own orders"""
    
    def has_permission(self, request, view):
        return request.user.is_authenticated and hasattr(request.user, 'customer')
    
    def has_object_permission(self, request, view, obj):
        return obj.customer == request.user.customer
```

#### 2.2 Updated Order ViewSet
Simplify the OrderViewSet to be customer-focused:

```python
class OrderViewSet(ModelViewSet):
    http_method_names = ['get', 'post', 'head', 'options']  # Remove delete, patch for customers
    permission_classes = [IsCustomerOwner]
    pagination_class = DefaultPagination
    
    def get_queryset(self):
        """Return only the authenticated customer's orders"""
        if not hasattr(self.request.user, 'customer'):
            return Order.objects.none()
        
        return Order.objects.filter(
            customer=self.request.user.customer
        ).select_related(
            'customer__user',
            'selected_address', 
            'payment_method'
        ).prefetch_related(
            Prefetch(
                'ordered_items',
                queryset=OrderItem.objects.select_related('product', 'product_variant')
                .prefetch_related(
                    'product_variant__price_label',
                    'product_variant__product_image'
                )
            )
        ).order_by('-placed_at')
```

### Phase 3: Implement Staff Order Management

#### 3.1 Staff App Order Models
**File**: `apps/staff/orders/models.py`

```python
from django.db import models
from apps.order.models import Order, OrderItem
from apps.staff.authorization.models import StaffProfile

class OrderProxy(Order):
    """Proxy model for staff-specific order operations"""
    
    class Meta:
        proxy = True
        verbose_name = "Staff Order"
        verbose_name_plural = "Staff Orders"

class OrderStatusHistory(models.Model):
    """Track order status changes by staff"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='status_history')
    previous_status = models.CharField(max_length=20)
    new_status = models.CharField(max_length=20)
    changed_by = models.ForeignKey(StaffProfile, on_delete=models.SET_NULL, null=True)
    changed_at = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True)

class OrderAssignment(models.Model):
    """Assign orders to staff members"""
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name='assignments')
    assigned_to = models.ForeignKey(StaffProfile, on_delete=models.CASCADE)
    assigned_by = models.ForeignKey(StaffProfile, on_delete=models.SET_NULL, null=True, related_name='assigned_orders')
    assigned_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)
```

#### 3.2 Staff Order Permissions
**File**: `apps/staff/orders/permissions.py`

```python
from rest_framework.permissions import BasePermission
from apps.staff.common.constants import STAFF_GROUPS

class CanManageOrders(BasePermission):
    """Permission for order management operations"""
    
    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check if user has order management permissions
        user_groups = set(request.user.groups.values_list('name', flat=True))
        allowed_groups = {
            STAFF_GROUPS['ORDER_MANAGER'],
            STAFF_GROUPS['ORDER_TEAM_MEMBER'], 
            STAFF_GROUPS['ORDER_FULFILLMENT']
        }
        
        return bool(user_groups.intersection(allowed_groups))

class CanChangeOrderStatus(BasePermission):
    """Permission for changing order status"""
    
    def has_permission(self, request, view):
        if not (request.user.is_authenticated and request.user.is_staff):
            return False
        
        if request.user.is_superuser:
            return True
        
        # Check specific permissions based on action
        if request.method in ['PATCH', 'PUT']:
            return request.user.has_perm('order.change_order')
        
        return True
```

#### 3.3 Staff Order ViewSets
**File**: `apps/staff/orders/views.py`

```python
from rest_framework.viewsets import ModelViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q, Prefetch
from apps.order.models import Order, OrderItem
from apps.staff.authorization.permissions import IsStaffUser
from .permissions import CanManageOrders, CanChangeOrderStatus
from .models import OrderProxy, OrderStatusHistory
from .serializers import StaffOrderSerializer, OrderStatusUpdateSerializer

class StaffOrderViewSet(ModelViewSet):
    """Staff-specific order management"""
    permission_classes = [IsStaffUser, CanManageOrders]
    serializer_class = StaffOrderSerializer
    
    def get_queryset(self):
        """Filter orders based on staff role"""
        user = self.request.user
        base_queryset = Order.objects.select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related(
            Prefetch(
                'ordered_items',
                queryset=OrderItem.objects.select_related('product', 'product_variant')
            )
        )
        
        if user.is_superuser:
            return base_queryset
        
        # Filter based on user's role and permissions
        user_groups = set(user.groups.values_list('name', flat=True))
        
        # Order Management Executive - all orders
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return base_queryset
        
        # Order Fulfillment Specialist - pending and processing orders
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            return base_queryset.filter(
                delivery_status__in=['Pending', 'Processing']
            )
        
        # Order Management Group Member - limited access
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            return base_queryset.filter(
                delivery_status__in=['Pending', 'Processing', 'Dispatched']
            )
        
        return Order.objects.none()
    
    @action(detail=True, methods=['patch'], permission_classes=[CanChangeOrderStatus])
    def update_status(self, request, pk=None):
        """Update order delivery status with audit trail"""
        order = self.get_object()
        serializer = OrderStatusUpdateSerializer(data=request.data)
        
        if serializer.is_valid():
            old_status = order.delivery_status
            new_status = serializer.validated_data['delivery_status']
            
            # Update order
            order.delivery_status = new_status
            order.save()
            
            # Create audit trail
            OrderStatusHistory.objects.create(
                order=order,
                previous_status=old_status,
                new_status=new_status,
                changed_by=request.user.staff_profile,
                notes=serializer.validated_data.get('notes', '')
            )
            
            return Response({'status': 'Order status updated successfully'})
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
```

### Phase 4: Update URLs and Configuration

#### 4.1 Customer Order URLs
**File**: `apps/order/urls.py` - Keep as is, but ensure it's customer-focused

#### 4.2 Staff Order URLs  
**File**: `apps/staff/orders/urls.py`

```python
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import StaffOrderViewSet

router = DefaultRouter()
router.register(r'orders', StaffOrderViewSet, basename='staff-orders')

app_name = 'orders'

urlpatterns = [
    path('', include(router.urls)),
]
```

## Implementation Checklist

### Phase 1: Cleanup (Priority: High) ✅ COMPLETED
- [x] Remove hardcoded group references from `apps/order/views.py`
- [x] Clean up `utils/permissions.py` to remove outdated group logic
- [x] Update order app to be customer-focused only
- [x] Create customer-focused permission classes
- [ ] Test customer order functionality

### Phase 2: Staff Implementation (Priority: High) ✅ COMPLETED
- [x] Create staff order models in `apps/staff/orders/models.py`
- [x] Implement staff order permissions in `apps/staff/orders/permissions.py`
- [x] Create staff order views in `apps/staff/orders/views.py`
- [x] Create staff order serializers
- [x] Update staff order URLs
- [x] Create pagination classes for staff operations

### Phase 3: Testing & Validation (Priority: High)
- [ ] Create and run migrations for staff order models
- [ ] Test customer order access (should only see own orders)
- [ ] Test staff order access with different roles
- [ ] Verify order status update functionality
- [ ] Test audit trail creation
- [ ] Validate permission boundaries

### Phase 4: Documentation (Priority: Medium)
- [ ] Update API documentation
- [ ] Create staff order management guide
- [ ] Document role-based access patterns
- [ ] Update frontend integration guides

## Migration Notes

1. **Backward Compatibility**: The cleanup will break existing staff workflows temporarily
2. **Data Migration**: No data migration needed, only code changes
3. **Testing**: Comprehensive testing required for both customer and staff flows
4. **Deployment**: Should be deployed as a single unit to avoid broken functionality

## Implementation Summary

### ✅ Completed Work

#### Customer Order App Cleanup
1. **Removed outdated role references**: Eliminated hardcoded `'OrderVerifiers'` and `'LogisticsCoordinators'` from order views
2. **Simplified permissions**: Replaced complex `IsAdminOrGroupMember` with customer-focused `IsCustomerOwner` permission
3. **Customer-focused API**: Order app now only serves customers, with simplified HTTP methods (GET, POST only)
4. **Clean queryset**: Customers can only access their own orders, no staff filtering logic

#### Staff Order Management Implementation
1. **Staff order models**: Created `OrderProxy`, `OrderStatusHistory`, `OrderAssignment`, and `OrderNote` models
2. **RBAC permissions**: Implemented role-based permissions using new RBAC groups (OME, OMGM, OFS)
3. **Comprehensive ViewSets**: Created staff order management with status updates, assignments, notes, and analytics
4. **Audit trail**: All staff actions on orders are logged with proper attribution
5. **Role-based filtering**: Different staff roles see different subsets of orders based on their permissions

#### Key Files Modified/Created
- `apps/order/views.py` - Simplified for customer use
- `apps/order/permissions.py` - New customer-focused permissions
- `apps/order/serializers.py` - Removed staff-specific serializers
- `utils/permissions.py` - Deprecated outdated permission class
- `apps/staff/orders/models.py` - Complete staff order models
- `apps/staff/orders/permissions.py` - RBAC-based permissions
- `apps/staff/orders/views.py` - Comprehensive staff order management
- `apps/staff/orders/serializers.py` - Staff-specific serializers
- `apps/staff/orders/urls.py` - Staff order API endpoints
- `apps/staff/common/pagination.py` - Staff pagination classes

## Success Criteria

1. ✅ No hardcoded group references in order app
2. ✅ Clear separation between customer and staff order operations
3. ✅ Staff order management uses new RBAC system
4. ✅ Audit trail for all staff order operations
5. ✅ Role-based filtering works correctly
6. ⏳ All tests pass for both customer and staff scenarios (pending testing)
