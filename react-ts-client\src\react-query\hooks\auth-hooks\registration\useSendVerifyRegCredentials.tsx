import { useMutation } from '@tanstack/react-query'
import APIClient from '../../../services/auth-client'
import { VerificationCredentials } from '../../../../pages/auth/register/2-verify-reg-credentials/VerifyRegCredentials'


const useSendVerifyRegCredentials = () => {
  const apiClient = new APIClient('/verify-code/')

  const mutation = useMutation({
    mutationFn: (data: VerificationCredentials) => apiClient.post(data)
  })

  return { mutation }
}

export default useSendVerifyRegCredentials
