# Generated by Django 5.1.6 on 2025-06-29 14:39

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0008_alter_productvariant_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Attribute Value (Individual)',
                'verbose_name_plural': 'Attribute Values (Individual)',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attributevalue',),
        ),
        migrations.CreateModel(
            name='ProductTypeAttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Product Type Attribute (Individual)',
                'verbose_name_plural': 'Product Type Attributes (Individual)',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttypeattribute',),
        ),
    ]
