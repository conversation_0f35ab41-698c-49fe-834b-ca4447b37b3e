from django.db import transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.conf import settings
from django.template.loader import render_to_string
from django.http import HttpResponse
import os
import json
from datetime import datetime
from apps.order.models import Order, OrderItem
from apps.staff.authorization.models import StaffProfile
from apps.staff.common.constants import STAFF_GROUPS
from .models import OrderStatusHistory, OrderAssignment, OrderNote, BulkOrderOperation, OrderDocument


class OrderService:
    """
    Business logic service for order management operations.
    Handles complex order operations with proper validation and audit trails.
    """

    @staticmethod
    def get_orders_for_user(user, filters=None):
        """
        Get orders based on user role and permissions.
        
        Args:
            user: The staff user requesting orders
            filters: Optional dictionary of filters to apply
            
        Returns:
            QuerySet of orders the user can access
        """
        base_queryset = Order.objects.select_related(
            'customer__user',
            'selected_address',
            'payment_method'
        ).prefetch_related('ordered_items')

        if user.is_superuser:
            queryset = base_queryset
        else:
            user_groups = set(user.groups.values_list('name', flat=True))
            
            # Order Management Executive - all orders
            if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
                queryset = base_queryset
            # Order Fulfillment Specialist - pending and processing orders
            elif STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
                queryset = base_queryset.filter(
                    delivery_status__in=['Pending', 'Processing']
                )
            # Order Management Group Member - limited access
            elif STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
                queryset = base_queryset.filter(
                    delivery_status__in=['Pending', 'Processing', 'Dispatched']
                )
            # Customer Service - can view orders for support
            elif STAFF_GROUPS['CUSTOMER_SERVICE'] in user_groups:
                queryset = base_queryset
            else:
                queryset = Order.objects.none()

        # Apply additional filters if provided
        if filters:
            if 'status' in filters:
                queryset = queryset.filter(delivery_status=filters['status'])
            if 'payment_status' in filters:
                queryset = queryset.filter(payment_status=filters['payment_status'])
            if 'customer_id' in filters:
                queryset = queryset.filter(customer_id=filters['customer_id'])
            if 'date_from' in filters:
                queryset = queryset.filter(placed_at__gte=filters['date_from'])
            if 'date_to' in filters:
                queryset = queryset.filter(placed_at__lte=filters['date_to'])

        return queryset.order_by('-placed_at')

    @staticmethod
    @transaction.atomic
    def update_order_status(order_id, new_status, user, reason=None):
        """
        Update order status with audit trail.
        
        Args:
            order_id: ID of the order to update
            new_status: New delivery status
            user: Staff user making the change
            reason: Optional reason for the status change
            
        Returns:
            Updated order instance
            
        Raises:
            ValidationError: If status transition is invalid
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        # Validate status transition
        valid_transitions = {
            'Pending': ['Processing'],
            'Processing': ['Dispatched'],
            'Dispatched': ['Delivered'],
            'Delivered': []  # No transitions from delivered
        }

        current_status = order.delivery_status
        if new_status not in valid_transitions.get(current_status, []):
            raise ValidationError(
                f"Cannot change status from '{current_status}' to '{new_status}'"
            )

        # Check if user has permission to make this change
        if not OrderService._can_change_status(user, order, new_status):
            raise ValidationError("Insufficient permissions to change order status")

        # Update order status
        previous_status = order.delivery_status
        order.delivery_status = new_status
        order.save()

        # Create status history record
        OrderStatusHistory.objects.create(
            order=order,
            previous_status=previous_status,
            new_status=new_status,
            changed_by=user.staff_profile,
            notes=reason or ''
        )

        return order

    @staticmethod
    def _can_change_status(user, order, new_status):
        """Check if user can change order to the specified status"""
        if user.is_superuser:
            return True

        user_groups = set(user.groups.values_list('name', flat=True))
        
        # Order Management Executive can change any status
        if STAFF_GROUPS['ORDER_MANAGER'] in user_groups:
            return True
        
        # Order Fulfillment Specialist can only change to Processing or Dispatched
        if STAFF_GROUPS['ORDER_FULFILLMENT'] in user_groups:
            return new_status in ['Processing', 'Dispatched']
        
        # Order Management Group Member can only change to Processing
        if STAFF_GROUPS['ORDER_TEAM_MEMBER'] in user_groups:
            return new_status == 'Processing'
        
        return False

    @staticmethod
    @transaction.atomic
    def assign_order(order_id, assigned_to_user, assigned_by_user, notes=None):
        """
        Assign order to staff member.
        
        Args:
            order_id: ID of the order to assign
            assigned_to_user: Staff user to assign the order to
            assigned_by_user: Staff user making the assignment
            notes: Optional assignment notes
            
        Returns:
            OrderAssignment instance
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        # Deactivate existing assignments
        order.assignments.filter(is_active=True).update(is_active=False)

        # Create new assignment
        assignment = OrderAssignment.objects.create(
            order=order,
            assigned_to=assigned_to_user.staff_profile,
            assigned_by=assigned_by_user.staff_profile,
            notes=notes or ''
        )

        return assignment

    @staticmethod
    @transaction.atomic
    def bulk_update_orders(order_ids, updates, user):
        """
        Perform bulk updates on multiple orders.
        
        Args:
            order_ids: List of order IDs to update
            updates: Dictionary of fields to update
            user: Staff user performing the update
            
        Returns:
            Number of orders updated
        """
        orders = Order.objects.filter(id__in=order_ids)
        updated_count = 0

        for order in orders:
            if 'delivery_status' in updates:
                try:
                    OrderService.update_order_status(
                        order.id,
                        updates['delivery_status'],
                        user,
                        updates.get('reason', 'Bulk update')
                    )
                    updated_count += 1
                except ValidationError:
                    # Skip orders that can't be updated
                    continue

        return updated_count

    @staticmethod
    def add_order_note(order_id, note_text, user, is_internal=True):
        """
        Add a note to an order.
        
        Args:
            order_id: ID of the order
            note_text: Text content of the note
            user: Staff user adding the note
            is_internal: Whether the note is internal only
            
        Returns:
            OrderNote instance
        """
        try:
            order = Order.objects.get(id=order_id)
        except Order.DoesNotExist:
            raise ValidationError("Order not found")

        note = OrderNote.objects.create(
            order=order,
            created_by=user.staff_profile,
            note=note_text,
            is_internal=is_internal
        )

        return note

    @staticmethod
    def get_order_analytics(user, date_from=None, date_to=None):
        """
        Get order analytics data for the user's accessible orders.
        
        Args:
            user: Staff user requesting analytics
            date_from: Optional start date filter
            date_to: Optional end date filter
            
        Returns:
            Dictionary with analytics data
        """
        orders = OrderService.get_orders_for_user(user)
        
        if date_from:
            orders = orders.filter(placed_at__gte=date_from)
        if date_to:
            orders = orders.filter(placed_at__lte=date_to)

        # Calculate basic metrics
        total_orders = orders.count()
        total_revenue = sum(order.total or 0 for order in orders)
        
        # Status breakdown
        status_counts = {}
        for status_choice in Order.DELIVERY_STATUS:
            status = status_choice[0]
            status_counts[status] = orders.filter(delivery_status=status).count()

        # Payment status breakdown
        payment_counts = {}
        for payment_choice in Order.PAYMENT_STATUS:
            status = payment_choice[0]
            payment_counts[status] = orders.filter(payment_status=status).count()

        return {
            'total_orders': total_orders,
            'total_revenue': total_revenue,
            'status_breakdown': status_counts,
            'payment_breakdown': payment_counts,
            'average_order_value': total_revenue / total_orders if total_orders > 0 else 0
        }

    @staticmethod
    @transaction.atomic
    def bulk_update_order_status(order_ids, new_status, user, reason=None):
        """
        Perform bulk status updates with proper tracking and validation.

        Args:
            order_ids: List of order IDs to update
            new_status: New delivery status
            user: Staff user performing the update
            reason: Optional reason for the status change

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=user,
            operation_type='BULK_STATUS_UPDATE',
            total_items=len(order_ids),
            operation_data={
                'order_ids': order_ids,
                'new_status': new_status,
                'reason': reason or 'Bulk status update'
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            updated_orders = []
            failed_orders = []

            for order_id in order_ids:
                try:
                    order = OrderService.update_order_status(
                        order_id, new_status, user, reason
                    )
                    updated_orders.append({
                        'order_id': order_id,
                        'previous_status': order.status_history.first().previous_status,
                        'new_status': new_status
                    })
                    bulk_op.increment_processed()

                except ValidationError as e:
                    failed_orders.append({
                        'order_id': order_id,
                        'error': str(e)
                    })
                    bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'updated_orders': updated_orders,
                'failed_orders': failed_orders,
                'success_count': len(updated_orders),
                'failure_count': len(failed_orders)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk status update failed: {str(e)}")

    @staticmethod
    @transaction.atomic
    def bulk_assign_orders(order_ids, assigned_to_user, assigned_by_user, notes=None):
        """
        Perform bulk order assignments.

        Args:
            order_ids: List of order IDs to assign
            assigned_to_user: Staff user to assign orders to
            assigned_by_user: Staff user making the assignments
            notes: Optional assignment notes

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=assigned_by_user,
            operation_type='BULK_ASSIGNMENT',
            total_items=len(order_ids),
            operation_data={
                'order_ids': order_ids,
                'assigned_to_id': assigned_to_user.staff_profile.id,
                'notes': notes or ''
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            assigned_orders = []
            failed_orders = []

            for order_id in order_ids:
                try:
                    assignment = OrderService.assign_order(
                        order_id, assigned_to_user, assigned_by_user, notes
                    )
                    assigned_orders.append({
                        'order_id': order_id,
                        'assignment_id': assignment.id,
                        'assigned_to': assigned_to_user.email
                    })
                    bulk_op.increment_processed()

                except ValidationError as e:
                    failed_orders.append({
                        'order_id': order_id,
                        'error': str(e)
                    })
                    bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'assigned_orders': assigned_orders,
                'failed_orders': failed_orders,
                'success_count': len(assigned_orders),
                'failure_count': len(failed_orders)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk assignment failed: {str(e)}")

    @staticmethod
    @transaction.atomic
    def bulk_generate_documents(order_ids, document_types, user, include_customer_invoice=False, include_warranty_info=False):
        """
        Generate documents for multiple orders.

        Args:
            order_ids: List of order IDs
            document_types: List of document types to generate
            user: Staff user generating documents
            include_customer_invoice: Whether to include customer invoice
            include_warranty_info: Whether to include warranty information

        Returns:
            BulkOrderOperation instance with results
        """
        import uuid

        # Create bulk operation record
        bulk_op = BulkOrderOperation.objects.create(
            operation_id=uuid.uuid4(),
            staff_user=user,
            operation_type='BULK_LABEL_PRINT' if 'SHIPPING_LABEL' in document_types else 'BULK_INVOICE_GENERATE',
            total_items=len(order_ids) * len(document_types),
            operation_data={
                'order_ids': order_ids,
                'document_types': document_types,
                'include_customer_invoice': include_customer_invoice,
                'include_warranty_info': include_warranty_info
            }
        )

        try:
            bulk_op.status = 'IN_PROGRESS'
            bulk_op.save()

            generated_documents = []
            failed_documents = []

            for order_id in order_ids:
                try:
                    order = Order.objects.get(id=order_id)

                    for doc_type in document_types:
                        try:
                            document = OrderService._generate_single_document(
                                order, doc_type, user, bulk_op, include_customer_invoice, include_warranty_info
                            )
                            generated_documents.append({
                                'order_id': order_id,
                                'document_id': document.id,
                                'document_type': doc_type,
                                'file_path': document.file_path
                            })
                            bulk_op.increment_processed()

                        except Exception as e:
                            failed_documents.append({
                                'order_id': order_id,
                                'document_type': doc_type,
                                'error': str(e)
                            })
                            bulk_op.increment_failed()

                except Order.DoesNotExist:
                    for doc_type in document_types:
                        failed_documents.append({
                            'order_id': order_id,
                            'document_type': doc_type,
                            'error': 'Order not found'
                        })
                        bulk_op.increment_failed()

            # Update operation results
            bulk_op.results = {
                'generated_documents': generated_documents,
                'failed_documents': failed_documents,
                'success_count': len(generated_documents),
                'failure_count': len(failed_documents)
            }

            if bulk_op.failed_items == 0:
                bulk_op.mark_completed()
            else:
                bulk_op.status = 'COMPLETED'
                bulk_op.completed_at = timezone.now()
                bulk_op.save()

            return bulk_op

        except Exception as e:
            bulk_op.mark_failed(str(e))
            raise ValidationError(f"Bulk document generation failed: {str(e)}")

    @staticmethod
    def _generate_single_document(order, document_type, user, bulk_operation=None, include_customer_invoice=False, include_warranty_info=False):
        """
        Generate a single document for an order with actual file creation.

        Args:
            order: Order instance
            document_type: Type of document to generate
            user: Staff user generating the document
            bulk_operation: Optional bulk operation instance
            include_customer_invoice: Whether to include customer invoice details
            include_warranty_info: Whether to include warranty information

        Returns:
            OrderDocument instance
        """
        # Generate document content based on type
        document_data = OrderService._prepare_document_data(
            order, document_type, include_customer_invoice, include_warranty_info
        )

        # Generate actual file
        file_path = OrderService._create_document_file(
            order, document_type, document_data
        )

        # Create document record
        document = OrderDocument.objects.create(
            order=order,
            document_type=document_type,
            generated_by=user.staff_profile,
            bulk_operation=bulk_operation,
            document_data=document_data,
            file_path=file_path
        )

        return document

    @staticmethod
    def _create_document_file(order, document_type, document_data):
        """
        Create actual document file (PDF/HTML) based on document type.

        Args:
            order: Order instance
            document_type: Type of document
            document_data: Document content data

        Returns:
            str: File path of created document
        """
        # Create documents directory if it doesn't exist
        base_dir = os.path.join(settings.MEDIA_ROOT, 'documents', document_type.lower())
        os.makedirs(base_dir, exist_ok=True)

        # Generate filename with timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"order_{order.id}_{document_type.lower()}_{timestamp}"

        if document_type in ['SHIPPING_LABEL', 'CUSTOMER_INVOICE', 'WAREHOUSE_PICKUP', 'PACKING_SLIP']:
            # Generate PDF for printable documents
            pdf_path = OrderService._generate_pdf_document(
                order, document_type, document_data, base_dir, filename
            )
            return pdf_path
        else:
            # Generate HTML for other documents
            html_path = OrderService._generate_html_document(
                order, document_type, document_data, base_dir, filename
            )
            return html_path

    @staticmethod
    def _generate_pdf_document(order, document_type, document_data, base_dir, filename):
        """
        Generate PDF document using reportlab or weasyprint.

        Args:
            order: Order instance
            document_type: Type of document
            document_data: Document content data
            base_dir: Base directory for saving
            filename: Base filename

        Returns:
            str: Relative file path
        """
        try:
            # Try to use reportlab for PDF generation
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.lib.units import inch
            from reportlab.lib import colors

            pdf_filename = f"{filename}.pdf"
            pdf_path = os.path.join(base_dir, pdf_filename)

            # Create PDF document
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            styles = getSampleStyleSheet()
            story = []

            # Add content based on document type
            if document_type == 'SHIPPING_LABEL':
                story.extend(OrderService._create_shipping_label_content(document_data, styles))
            elif document_type == 'CUSTOMER_INVOICE':
                story.extend(OrderService._create_invoice_content(document_data, styles))
            elif document_type == 'WAREHOUSE_PICKUP':
                story.extend(OrderService._create_warehouse_pickup_content(document_data, styles))
            elif document_type == 'PACKING_SLIP':
                story.extend(OrderService._create_packing_slip_content(document_data, styles))

            # Build PDF
            doc.build(story)

            # Return relative path
            return f"documents/{document_type.lower()}/{pdf_filename}"

        except ImportError:
            # Fallback to HTML if reportlab is not available
            return OrderService._generate_html_document(order, document_type, document_data, base_dir, filename)
        except Exception as e:
            # Log error and create simple text file
            txt_filename = f"{filename}_error.txt"
            txt_path = os.path.join(base_dir, txt_filename)

            with open(txt_path, 'w') as f:
                f.write(f"Error generating PDF: {str(e)}\n\n")
                f.write(f"Document Data:\n{json.dumps(document_data, indent=2)}")

            return f"documents/{document_type.lower()}/{txt_filename}"

    @staticmethod
    def _generate_html_document(order, document_type, document_data, base_dir, filename):
        """
        Generate HTML document as fallback or for web display.

        Args:
            order: Order instance
            document_type: Type of document
            document_data: Document content data
            base_dir: Base directory for saving
            filename: Base filename

        Returns:
            str: Relative file path
        """
        html_filename = f"{filename}.html"
        html_path = os.path.join(base_dir, html_filename)

        # Generate HTML content
        html_content = OrderService._create_html_content(document_type, document_data)

        # Save HTML file
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        return f"documents/{document_type.lower()}/{html_filename}"

    @staticmethod
    def _create_shipping_label_content(document_data, styles):
        """Create shipping label PDF content"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        story = []

        # Title
        title_style = styles['Title']
        story.append(Paragraph("SHIPPING LABEL", title_style))
        story.append(Spacer(1, 0.2*inch))

        # Shipping info table
        shipping_data = [
            ['Order ID:', document_data['order_id']],
            ['Tracking:', document_data.get('tracking_number', 'N/A')],
            ['Weight:', f"{document_data.get('weight', 0)} kg"],
            ['Shipping Cost:', f"${document_data.get('shipping_cost', '0.00')}"],
        ]

        shipping_table = Table(shipping_data, colWidths=[2*inch, 3*inch])
        shipping_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(shipping_table)
        story.append(Spacer(1, 0.3*inch))

        # Customer info
        story.append(Paragraph("SHIP TO:", styles['Heading2']))
        story.append(Paragraph(f"<b>{document_data['customer_name']}</b>", styles['Normal']))

        if document_data.get('delivery_address'):
            addr = document_data['delivery_address']
            address_text = f"{addr['street']}<br/>{addr['city']}, {addr['state']} {addr['postal_code']}<br/>{addr['country']}"
            story.append(Paragraph(address_text, styles['Normal']))

        return story

    @staticmethod
    def _create_invoice_content(document_data, styles):
        """Create customer invoice PDF content"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        story = []

        # Invoice header
        story.append(Paragraph("CUSTOMER INVOICE", styles['Title']))
        story.append(Spacer(1, 0.2*inch))

        # Invoice details
        invoice_data = [
            ['Invoice Number:', document_data.get('invoice_number', f"INV{document_data['order_id']:08d}")],
            ['Order Date:', document_data['order_date'][:10]],
            ['Payment Status:', document_data.get('payment_status', 'N/A')],
            ['Payment Method:', document_data.get('payment_method', 'N/A')],
        ]

        invoice_table = Table(invoice_data, colWidths=[2*inch, 3*inch])
        invoice_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightblue),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
        ]))

        story.append(invoice_table)
        story.append(Spacer(1, 0.3*inch))

        # Items table
        story.append(Paragraph("ORDER ITEMS:", styles['Heading2']))

        items_data = [['Product', 'Quantity', 'Price', 'Total']]
        for item in document_data['items']:
            items_data.append([
                item['product_name'],
                str(item['quantity']),
                f"${item['price']}",
                f"${float(item['price']) * item['quantity']:.2f}"
            ])

        # Add totals
        items_data.append(['', '', 'Subtotal:', f"${document_data.get('subtotal', '0.00')}"])
        items_data.append(['', '', 'Shipping:', f"${document_data.get('shipping_cost', '0.00')}"])
        items_data.append(['', '', 'TOTAL:', f"${document_data['total_amount']}"])

        items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1*inch, 1*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -4), colors.beige),
            ('BACKGROUND', (0, -3), (-1, -1), colors.lightgrey),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(items_table)

        # Warranty info if requested
        if document_data.get('include_warranty') and document_data.get('warranty_info'):
            story.append(Spacer(1, 0.3*inch))
            story.append(Paragraph("WARRANTY INFORMATION:", styles['Heading2']))
            warranty = document_data['warranty_info']
            story.append(Paragraph(f"Period: {warranty['warranty_period']}", styles['Normal']))
            story.append(Paragraph(f"Terms: {warranty['warranty_terms']}", styles['Normal']))
            story.append(Paragraph(f"Support: {warranty['support_contact']}", styles['Normal']))

        return story

    @staticmethod
    def _create_warehouse_pickup_content(document_data, styles):
        """Create warehouse pickup document PDF content"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        story = []

        # Header
        story.append(Paragraph("WAREHOUSE PICKUP DOCUMENT", styles['Title']))
        story.append(Spacer(1, 0.2*inch))

        # Pickup details
        pickup_data = [
            ['Pickup Number:', document_data.get('pickup_number', f"PKP{document_data['order_id']:08d}")],
            ['Order ID:', document_data['order_id']],
            ['Priority:', document_data.get('priority', 'NORMAL')],
            ['Customer:', document_data['customer_name']],
        ]

        pickup_table = Table(pickup_data, colWidths=[2*inch, 3*inch])
        pickup_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.orange),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
        ]))

        story.append(pickup_table)
        story.append(Spacer(1, 0.3*inch))

        # Items to pick
        story.append(Paragraph("ITEMS TO PICK:", styles['Heading2']))

        items_data = [['Product', 'SKU', 'Quantity', 'Location']]
        for item in document_data['items']:
            location = f"Section {item['product_name'][:3].upper()}"  # Simple location logic
            items_data.append([
                item['product_name'],
                item.get('sku', 'N/A'),
                str(item['quantity']),
                location
            ])

        items_table = Table(items_data, colWidths=[2.5*inch, 1.5*inch, 1*inch, 1*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(items_table)

        # Special instructions
        if document_data.get('special_instructions'):
            story.append(Spacer(1, 0.3*inch))
            story.append(Paragraph("SPECIAL INSTRUCTIONS:", styles['Heading2']))
            story.append(Paragraph(document_data['special_instructions'], styles['Normal']))

        return story

    @staticmethod
    def _create_packing_slip_content(document_data, styles):
        """Create packing slip PDF content"""
        from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
        from reportlab.lib import colors
        from reportlab.lib.units import inch

        story = []

        # Header
        story.append(Paragraph("PACKING SLIP", styles['Title']))
        story.append(Spacer(1, 0.2*inch))

        # Packing details
        packing_data = [
            ['Packing Slip:', document_data.get('packing_slip_number', f"PKS{document_data['order_id']:08d}")],
            ['Order ID:', document_data['order_id']],
            ['Customer:', document_data['customer_name']],
            ['Instructions:', document_data.get('packing_instructions', 'Standard packing')],
        ]

        packing_table = Table(packing_data, colWidths=[2*inch, 3*inch])
        packing_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightcyan),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
        ]))

        story.append(packing_table)
        story.append(Spacer(1, 0.3*inch))

        # Items checklist
        story.append(Paragraph("PACKING CHECKLIST:", styles['Heading2']))

        items_data = [['☐', 'Product', 'Quantity', 'Notes']]
        for item in document_data['items']:
            notes = "FRAGILE" if item['product_name'] in document_data.get('fragile_items', []) else ""
            items_data.append([
                '☐',  # Checkbox for manual checking
                item['product_name'],
                str(item['quantity']),
                notes
            ])

        items_table = Table(items_data, colWidths=[0.5*inch, 2.5*inch, 1*inch, 2*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.purple),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))

        story.append(items_table)

        return story

    @staticmethod
    def _create_html_content(document_type, document_data):
        """Create HTML content for documents"""
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{document_type.replace('_', ' ').title()}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ text-align: center; color: #333; border-bottom: 2px solid #333; padding-bottom: 10px; }}
                .section {{ margin: 20px 0; }}
                .label {{ font-weight: bold; }}
                table {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                .total {{ font-weight: bold; background-color: #e6f3ff; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>{document_type.replace('_', ' ').title()}</h1>
                <p>Order ID: {document_data['order_id']} | Customer: {document_data['customer_name']}</p>
            </div>

            <div class="section">
                <h2>Order Details</h2>
                <p><span class="label">Order Date:</span> {document_data['order_date']}</p>
                <p><span class="label">Total Amount:</span> ${document_data['total_amount']}</p>
            </div>

            <div class="section">
                <h2>Items</h2>
                <table>
                    <tr><th>Product</th><th>Quantity</th><th>Price</th></tr>
        """

        for item in document_data['items']:
            html_template += f"""
                    <tr>
                        <td>{item['product_name']}</td>
                        <td>{item['quantity']}</td>
                        <td>${item['price']}</td>
                    </tr>
            """

        html_template += """
                </table>
            </div>

            <div class="section">
                <p><em>Generated on: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</em></p>
            </div>
        </body>
        </html>
        """

        return html_template

    @staticmethod
    def print_documents(documents, printer_name='default'):
        """
        Send documents to printer.

        Args:
            documents: QuerySet of OrderDocument instances
            printer_name: Name of the printer to use

        Returns:
            dict: Print results with success/failure counts
        """
        print_results = {
            'successful_prints': [],
            'failed_prints': [],
            'total_documents': documents.count()
        }

        for document in documents:
            try:
                # Check if file exists
                if not document.file_path:
                    print_results['failed_prints'].append({
                        'document_id': document.id,
                        'error': 'No file path specified'
                    })
                    continue

                file_path = os.path.join(settings.MEDIA_ROOT, document.file_path)

                if not os.path.exists(file_path):
                    print_results['failed_prints'].append({
                        'document_id': document.id,
                        'error': 'Physical file not found'
                    })
                    continue

                # Try different printing methods based on OS
                success = OrderService._send_to_printer(file_path, printer_name, document.document_type)

                if success:
                    print_results['successful_prints'].append({
                        'document_id': document.id,
                        'document_type': document.document_type,
                        'file_path': document.file_path
                    })
                else:
                    print_results['failed_prints'].append({
                        'document_id': document.id,
                        'error': 'Failed to send to printer'
                    })

            except Exception as e:
                print_results['failed_prints'].append({
                    'document_id': document.id,
                    'error': str(e)
                })

        return print_results

    @staticmethod
    def _send_to_printer(file_path, printer_name, document_type):
        """
        Send file to printer using OS-specific commands.

        Args:
            file_path: Path to the file to print
            printer_name: Name of the printer
            document_type: Type of document (for print settings)

        Returns:
            bool: True if successful, False otherwise
        """
        import subprocess
        import platform

        try:
            system = platform.system().lower()

            if system == 'windows':
                # Windows printing using PowerShell
                if file_path.endswith('.pdf'):
                    # For PDF files, use Adobe Reader or similar
                    cmd = [
                        'powershell', '-Command',
                        f'Start-Process -FilePath "{file_path}" -ArgumentList "/t" -WindowStyle Hidden'
                    ]
                else:
                    # For other files, use default print command
                    cmd = ['powershell', '-Command', f'Get-Content "{file_path}" | Out-Printer -Name "{printer_name}"']

            elif system == 'darwin':  # macOS
                # macOS printing using lpr
                cmd = ['lpr', '-P', printer_name, file_path]

            elif system == 'linux':
                # Linux printing using lpr
                cmd = ['lpr', '-P', printer_name, file_path]

            else:
                # Unsupported system
                return False

            # Execute print command
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            # Check if command was successful
            return result.returncode == 0

        except subprocess.TimeoutExpired:
            # Print command timed out
            return False
        except Exception:
            # Any other error
            return False

    @staticmethod
    def get_available_printers():
        """
        Get list of available printers on the system.

        Returns:
            list: List of available printer names
        """
        import subprocess
        import platform

        try:
            system = platform.system().lower()

            if system == 'windows':
                # Windows: Get printers using PowerShell
                cmd = ['powershell', '-Command', 'Get-Printer | Select-Object Name']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[2:]  # Skip header lines
                    printers = [line.strip() for line in lines if line.strip()]
                    return printers

            elif system in ['darwin', 'linux']:
                # macOS/Linux: Get printers using lpstat
                cmd = ['lpstat', '-p']
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

                if result.returncode == 0:
                    printers = []
                    for line in result.stdout.split('\n'):
                        if line.startswith('printer '):
                            printer_name = line.split()[1]
                            printers.append(printer_name)
                    return printers

            return ['default']  # Fallback

        except Exception:
            return ['default']  # Fallback on error

    @staticmethod
    def _prepare_document_data(order, document_type, include_customer_invoice=False, include_warranty_info=False):
        """
        Prepare document data based on document type.

        Args:
            order: Order instance
            document_type: Type of document
            include_customer_invoice: Whether to include customer invoice details
            include_warranty_info: Whether to include warranty information

        Returns:
            Dictionary with document data
        """
        base_data = {
            'order_id': order.id,
            'customer_name': f"{order.customer.first_name} {order.customer.last_name}",
            'customer_email': order.customer.user.email,
            'order_date': order.placed_at.isoformat(),
            'total_amount': str(order.total),
            'delivery_address': {
                'street': order.selected_address.street_address,
                'city': order.selected_address.city,
                'state': order.selected_address.state,
                'postal_code': order.selected_address.postal_code,
                'country': order.selected_address.country,
            } if order.selected_address else None,
            'items': [
                {
                    'product_name': item.product.title,
                    'variant': item.product_variant.title if item.product_variant else None,
                    'quantity': item.quantity,
                    'price': str(item.total_price),
                    'sku': item.product_variant.sku if item.product_variant else None,
                }
                for item in order.ordered_items.all()
            ]
        }

        if document_type == 'SHIPPING_LABEL':
            base_data.update({
                'tracking_number': f"TRK{order.id:08d}",
                'shipping_method': order.payment_method.name,
                'weight': order.total_weight,
                'shipping_cost': str(order.shipping_cost),
            })

        elif document_type == 'CUSTOMER_INVOICE':
            base_data.update({
                'invoice_number': f"INV{order.id:08d}",
                'payment_status': order.payment_status,
                'payment_method': order.payment_method.name,
                'subtotal': str(order.subtotal),
                'shipping_cost': str(order.shipping_cost),
                'include_warranty': include_warranty_info,
                'warranty_info': {
                    'warranty_period': '1 year',
                    'warranty_terms': 'Standard manufacturer warranty applies',
                    'support_contact': '<EMAIL>'
                } if include_warranty_info else None
            })

        elif document_type == 'WAREHOUSE_PICKUP':
            base_data.update({
                'pickup_number': f"PKP{order.id:08d}",
                'priority': 'HIGH' if order.total > 500 else 'NORMAL',
                'special_instructions': 'Handle with care' if any(
                    'fragile' in item.extra_data.get('notes', '').lower()
                    for item in order.ordered_items.all()
                ) else None,
                'warehouse_sections': [
                    f"Section {item.product.category.title[:3].upper()}"
                    for item in order.ordered_items.all()
                ]
            })

        elif document_type == 'PACKING_SLIP':
            base_data.update({
                'packing_slip_number': f"PKS{order.id:08d}",
                'packing_instructions': 'Standard packing',
                'fragile_items': [
                    item.product.title for item in order.ordered_items.all()
                    if 'fragile' in item.extra_data.get('notes', '').lower()
                ]
            })

        return base_data
