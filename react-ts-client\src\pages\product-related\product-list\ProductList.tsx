import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON> } from "react-icons/fa"
import { useParams, useSearchParams } from "react-router-dom"
import FilterOptions from "../../../components/filters/FilterOptions"
import SimpleProductCard from "../../../components/product/simple-product-card/SimpleProductCard"
import Sorting from "../../../components/sorting/SortBy"
import Alert from "../../../components/utils/alert/Alert"
import Pagination from "../../../components/utils/pagination/Pagination"
import Spinner from "../../../components/utils/spinner/Spinner"
import { ITEMS_PER_PAGE } from "../../../react-query/hooks/constants"
import useProducts from "../../../react-query/hooks/product-hooks/useProducts"
import { ProductShape } from "../../../types/product-types"
import filterStore from "../../../zustand/filterStore"
import styles from './ProductList.module.scss'


const ProductList = () => {
  const { slug } = useParams()

  const [showSorting, setShowSorting] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const { setProductTypeId, resetFilters } = filterStore()

  const [searchParams, setSearchParams] = useSearchParams()
  const page = parseInt(searchParams.get('page') || '1', 10)
  const searchQuery = searchParams.get('search') || ''

  const { isPending, error, data: products } = useProducts(slug!, page, searchParams.toString(), searchQuery)

  // Reset filters when the category changes
  useEffect(() => {
    resetFilters()
    setShowFilters(false)
    setShowSorting(false)
  }, [slug, resetFilters])

  // Set the product type ID when products are loaded
  useEffect(() => {
    if (products?.results.length) {
      setProductTypeId(products.results[0].product_type)
    }
  }, [products, setProductTypeId])

  const toggleFilters = () => setShowFilters(!showFilters)
  const toggleSorting = () => setShowSorting(!showSorting)

  const handlePageChange = (newPage: number) => {
    setSearchParams({ ...Object.fromEntries(searchParams), page: newPage.toString() })
  }
  
  return (
    <div className='container'>
      <div className={styles.product_list__container}>
        {/* Mobile controls for filters and sorting */}
        <div className={styles.mobile_controls}>
          <button onClick={toggleFilters}>
            {!showFilters ? <><FaFilter /> Filters</> : <span>Close Filters</span>}
          </button>
          {products && products.results.length > 1 && <button onClick={toggleSorting}>
            <FaSort /> Sort
          </button>}
        </div>

        {/* Filters section */}
        <section className={`${styles.filters} ${showFilters ? styles.show : ''}`}>
          <FilterOptions />
        </section>

        <section className={styles.product_list__wrapper}>
          {/* Sorting section */}
          <div className={`${styles.sorting} ${showSorting ? styles.show : ''}`}>
            {products && products.results.length > 1 && <Sorting />}
          </div>

          {/* Product list */}
          {isPending ? (
            <Spinner color='#0091CF' size={20} loading={true} />
          ) : error ? (
            <Alert variant='error' message={error.message} />
          ) : (
            <>
              <ul className={styles.product_list}>
                {products?.results.length === 0 ? (
                  <p className={styles.empty_products}>No products found.</p>
                ) : (
                  products?.results.map((product: ProductShape) => (
                    <li key={product.slug}>
                      <SimpleProductCard product={product} />
                    </li>
                  ))
                )}
              </ul>
              {/* Add Pagination */}
              {products && products.count > 0 && (
                <Pagination
                  currentPage={page}
                  totalPages={Math.ceil(products.count / ITEMS_PER_PAGE)} // Assuming 10 items per page
                  onPageChange={handlePageChange}
                />
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

export default ProductList