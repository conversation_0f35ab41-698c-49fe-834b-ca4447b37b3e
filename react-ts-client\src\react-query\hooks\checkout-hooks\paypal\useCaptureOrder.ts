import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"

interface CaptureOrderRequest {
  paypal_order_id: string
}

interface CaptureOrderResponse {
  status: string
  payment_status: string
}

const useCaptureOrder = () => {
  const apiClient = new APIClient<CaptureOrderResponse, CaptureOrderRequest>('/payments/capture-paypal-order/')

  return useMutation({
    mutationFn: (data: CaptureOrderRequest) => apiClient.post(data)
  })
}

export default useCaptureOrder
