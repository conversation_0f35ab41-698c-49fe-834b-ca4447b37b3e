import { useQuery } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"
import { CUSTOMER_ADDRESSES } from '../../constants'
import { AddressFormInputs } from '../../../../pages/auth/profile/addresses/ManageAddresses'


const useAddresses = () => {

  const apiClient = new APIClient<AddressFormInputs[]>('/customers/addresses/')

  return useQuery({
    queryKey: [CUSTOMER_ADDRESSES],
    queryFn: () => apiClient.get(),

    // keepPreviousData: true,
    // refetchOnWindowFocus: true,
    // staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
    // initialData:  Here we can add categories as static data
    // refetchOnMount: true,
  })
}

export default useAddresses
