import { FaCaret<PERSON><PERSON>, FaCartPlus, FaHeart } from "react-icons/fa"
import { MdOutlineAccountCircle } from "react-icons/md"
import { BsBox2 } from "react-icons/bs"
import { RiLogoutCircleRLine } from "react-icons/ri"
import Logo from "../utils/logo/Logo"
import Navbar from "./navbar/Navbar"
import Search from "./search-bar/Search"
import { useEffect, useState } from "react"
import { Link } from "react-router-dom"
import useCart from "../../react-query/hooks/cart-hooks/useCart"
import useCustomerDetails from "../../react-query/hooks/customer-related-hooks/useCustomerDetails"
import useLogout from "../../react-query/hooks/auth-hooks/useLogOut"
import authStore from "../../zustand/authStore"
import { CartItemShape } from "../../types/store-types"
import styles from "./Header.module.scss"
import useWishlist from "../../react-query/hooks/wishlist-hooks/useWishlist"


const Header = () => {
  const [isOpen, setIsOpen] = useState(false)
  const { data } = useCart()
  const { isLoggedIn } = authStore()

  const { mutation } = useLogout()
  const { data: wishlistItems } = useWishlist(1, isLoggedIn)
  const { data: customerData } = useCustomerDetails(isLoggedIn)

  const handleLinkClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if ((event.target as HTMLElement).tagName === "A") {
      setIsOpen(false)
    }
  }

  const handleLogout = () => {
    mutation.mutate()
  }

  const cartItemsQuantity = (cart_items: CartItemShape[]) => {
    let item_qty = 0
    if (cart_items) {
      cart_items.forEach((item) => {
        item_qty += item.quantity
      })
    }
    return item_qty
  }
  useEffect(() => {
    console.log(customerData)
    console.log(isLoggedIn)
  }, [customerData, isLoggedIn])
  return (
    <>
      <div className={styles.header}>
        <section className={`${styles.header__top} container`}>
          <section className={styles.header__logo}>
            <Logo />
          </section>
          <section className={styles.header__search}>
            <Search />
          </section>
          <section className={styles.header__end}>
            <div className={styles.wishlist}>
              <p className={styles.header__badge}>
                <span>{wishlistItems?.count || 0}</span>
              </p>
              <i className={styles.header__icon}><FaHeart /></i>
              <Link to='/customer/wishlist/'>Wishlist {`|`}</Link>
            </div>

            <div className={styles.cart}>
              <p className={styles.header__badge}><span>{cartItemsQuantity(data?.cart_items || [])}</span></p>
              <i className={styles.header__icon}><FaCartPlus /></i>
              <Link to='/cart/'>Cart {`|`}</Link>
            </div>
            <div className={styles.header__sign_in}>
              <div
                className={styles.header__login}
                onMouseEnter={() => setIsOpen(true)}
                onMouseLeave={() => setIsOpen(false)}
              >
                <p>Hello, {customerData?.first_name ? (
                  <>{customerData?.first_name}</>
                ) : isLoggedIn && !customerData?.first_name ? (
                  <>{customerData?.email}</>
                ) : (
                  <Link to='/user/login/'>Sign In</Link>
                )}
                </p>
                {isLoggedIn && <i><FaCaretDown /></i>}
              </div>
              {isOpen && isLoggedIn &&
                <div
                  className={`${styles.dropdown_container} ${isOpen ? styles.active : ''}`}
                  onMouseEnter={() => setIsOpen(true)}
                  onMouseLeave={() => setIsOpen(false)}
                  onClick={handleLinkClick}
                >
                  <div className={styles.dropdown}>
                    <div className={styles.dropdown_header}>
                      <h4>Welcome back, {customerData?.first_name || 'User'}</h4>
                    </div>
                    <div className={styles.dropdown_menu}>
                      <div className={styles.menu_item}>
                        <i><MdOutlineAccountCircle /></i>
                        <Link to='/customer/'>My Account</Link>
                      </div>
                      <div className={styles.menu_item}>
                        <i><BsBox2 /></i>
                        <Link to='/my-orders'>My Orders</Link>
                      </div>
                      {/* <div className={styles.divider}></div>
                      <div className={styles.menu_item}>
                        <i><BsBox2 /></i>
                        <Link to='/'>My Reviews</Link>
                      </div> */}
                    </div>
                    <div className={styles.dropdown_footer}>
                      <div className={styles.menu_item} onClick={handleLogout}>
                        <i><RiLogoutCircleRLine /></i>
                        <span>Sign Out</span>
                      </div>
                    </div>
                  </div>
                </div>
              }
            </div>
          </section>
        </section>
      </div>
      <div className={styles.header__bottom_nav}>
        <section className={`${styles.header__bottom} container`}>
          <Navbar />
        </section>
      </div>
    </>
  )
}

export default Header
