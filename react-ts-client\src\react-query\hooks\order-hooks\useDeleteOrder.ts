import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useState } from 'react'
import { CACHE_KEY_ORDERS } from "../constants"
import APIClient from "../../services/api-client"


const useDeleteOrder = () => {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [orderToDelete, setOrderToDelete] = useState<number | null>(null)
  const queryClient = useQueryClient()
  const apiClient = new APIClient(`/orders`)

  const deleteOrder = useMutation({
    mutationFn: (orderId: number) => apiClient.delete(orderId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEY_ORDERS]
      })
    },
  })

  const handleDeleteClick = (orderId: number) => {
    setOrderToDelete(orderId)
    setIsModalOpen(true)
  }

  const confirmDelete = () => {
    if (orderToDelete !== null) {
      deleteOrder.mutate(orderToDelete)
      setIsModalOpen(false)
      setOrderToDelete(null)
    }
  }

  const cancelDelete = () => {
    setIsModalOpen(false)
    setOrderToDelete(null)
  }

  return {
    deleteOrder,
    isModalOpen,
    handleDeleteClick,
    confirmDelete,
    cancelDelete
  }
}

export default useDeleteOrder
