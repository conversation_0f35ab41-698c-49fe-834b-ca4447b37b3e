import { ProductShape, ProductVariant } from "../types/product-types"

/**
 * Interface for a grouped variant
 * Represents a unique primary attribute value (e.g., a specific size)
 * with all its associated variants that share this primary attribute value
 */
export interface GroupedVariant {
  // The primary attribute value (e.g., "S", "M", "L")
  primaryValue: string
  // The ID of the primary attribute value
  primaryValueId: number
  // The price of this variant group (all variants in a group typically have the same price)
  price: number
  // The order of this variant group (used for sorting)
  order: number
  // All product variants that share this primary attribute value
  variants: ProductVariant[]
}

/**
 * Interface for secondary attribute options
 * Represents available values for a secondary attribute (e.g., available colors for a selected size)
 */
export interface SecondaryAttributeOption {
  attributeId: number
  attributeTitle: string
  values: {
    valueId: number
    valueText: string
    available: boolean // Whether this value is available for the selected primary attribute
  }[]
}

/**
 * Groups product variants by their primary attribute (the one used as price_label)
 * This eliminates duplicate display of the same primary attribute value
 *
 * @param product The product object containing variants and option selectors
 * @returns An array of grouped variants, each representing a unique primary attribute value, sorted by order
 */
export const groupVariantsByPrimaryAttribute = (product: ProductShape): GroupedVariant[] => {
  if (!product?.product_variant || product.product_variant.length === 0) {
    return []
  }

  // Get the primary attribute title (the one used for price_label)
  const primaryAttributeTitle = product.product_variant[0]?.price_label_attr_title
  if (!primaryAttributeTitle) {
    return []
  }

  // Sort variants by order first
  const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)

  // Create a map to group variants by their primary attribute value
  const variantGroups: Record<string, GroupedVariant> = {}

  // Group variants by their primary attribute value
  sortedVariants.forEach(variant => {
    const primaryValue = variant.price_label

    // Find the primary attribute value ID from the variant's attribute_value array
    const primaryAttributeValue = variant.attribute_value.find(
      attr => attr.attribute.title.toLowerCase() === primaryAttributeTitle.toLowerCase()
    )

    const primaryValueId = primaryAttributeValue?.id || 0

    // If this primary value hasn't been seen before, create a new group
    if (!variantGroups[primaryValue]) {
      variantGroups[primaryValue] = {
        primaryValue,
        primaryValueId,
        price: variant.price,
        order: variant.order, // Use the order from the first variant in this group
        variants: []
      }
    }

    // Add this variant to its group
    variantGroups[primaryValue].variants.push(variant)
  })

  // Convert the map to an array and sort by the order field
  return Object.values(variantGroups).sort((a, b) => a.order - b.order)
}

/**
 * Gets available secondary attribute options for a selected primary attribute value
 *
 * @param product The product object containing variants and option selectors
 * @param selectedPrimaryValue The selected primary attribute value
 * @returns An array of secondary attribute options with availability information
 */
export const getSecondaryAttributeOptions = (
  product: ProductShape,
  selectedPrimaryValue: string
): SecondaryAttributeOption[] => {
  if (!product?.option_selectors || !selectedPrimaryValue) {
    return []
  }

  // Get the primary attribute title
  const primaryAttributeTitle = product.product_variant[0]?.price_label_attr_title
  if (!primaryAttributeTitle) {
    return []
  }

  // Filter out the primary attribute from option selectors
  const secondaryAttributeSelectors = product.option_selectors.filter(
    selector => selector.attribute_title.toLowerCase() !== primaryAttributeTitle.toLowerCase()
  )

  // Sort variants by order first, then find variants that match the selected primary value
  const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)
  const matchingVariants = sortedVariants.filter(
    variant => variant.price_label === selectedPrimaryValue
  )

  // For each secondary attribute, determine which values are available
  return secondaryAttributeSelectors.map(selector => {
    // Get all possible values for this attribute
    const allValues = selector.values

    // For each value, check if it's available in any of the matching variants
    const valuesWithAvailability = allValues.map(value => {
      // Check if any matching variant has this attribute value
      const available = matchingVariants.some(variant => {
        return variant.attribute_value.some(attr =>
          attr.attribute.title.toLowerCase() === selector.attribute_title.toLowerCase() &&
          attr.attribute_value.toLowerCase() === value.value_text.toLowerCase()
        )
      })

      return {
        valueId: value.value_id,
        valueText: value.value_text,
        available
      }
    })

    return {
      attributeId: selector.attribute_id,
      attributeTitle: selector.attribute_title,
      values: valuesWithAvailability
    }
  })
}

/**
 * Finds the matching variant based on selected attribute values
 *
 * @param product The product object containing variants
 * @param selectedAttributes Record of selected attribute values (attribute title -> selected value)
 * @returns The matching variant or undefined if no match is found
 */
export const findMatchingVariant = (
  product: ProductShape,
  selectedAttributes: Record<string, string>
): ProductVariant | undefined => {
  if (!product?.product_variant || Object.keys(selectedAttributes).length === 0) {
    return undefined
  }

  // Sort variants by order first to ensure consistent selection
  const sortedVariants = [...product.product_variant].sort((a, b) => a.order - b.order)

  // Find a variant that matches all selected attribute values
  return sortedVariants.find(variant => {
    // Check if this variant matches all selected attributes
    return Object.entries(selectedAttributes).every(([attrTitle, attrValue]) => {
      // Find the matching attribute in this variant
      return variant.attribute_value.some(attr =>
        attr.attribute.title.toLowerCase() === attrTitle.toLowerCase() &&
        attr.attribute_value.toLowerCase() === attrValue.toLowerCase()
      )
    })
  })
}
