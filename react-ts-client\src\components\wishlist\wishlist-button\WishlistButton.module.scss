@import '../../../scss/mixins';
@import '../../../scss/variables';


.wishlist__button {
  @include btn(#fff, $primary-blue);
  background: none !important;
  border: 2px solid $primary-blue;
  // max-width: 50px !important;
  height: 40px;
  padding: 0 5px !important;
  // transition: color all 2s ease-in-out;
  margin: 0;

  @media (max-width: 450px) {
    max-width: 100% !important;
    width: 100%;
  }

  i {
    font-size: 24px;
    color: $primary-blue;
    @include flexbox(center, center);
    width: 100%;
    height: 100%;
  }

  &:hover {
    border-color: darken($lighten-blue, 15%);

    i {
      color: darken($lighten-blue, 15%);
    }
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}