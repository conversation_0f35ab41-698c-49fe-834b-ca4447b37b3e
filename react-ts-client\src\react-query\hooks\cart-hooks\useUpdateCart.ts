import { useMutation, useQueryClient } from '@tanstack/react-query'
import cartStore from "../../../zustand/cartStore"
import APIClient from "../../services/api-client"
import { CACHE_KEY_CART_ITEMS } from "../constants"
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../types/types'

export interface UpdateQtyShape {
  // itemId: number
  quantity: number
}

const useUpdateCart = () => {
  const { cartId } = cartStore()
  const queryClient = useQueryClient()

  const handleQuantityUpdate = (itemId: number, newQuantity: number) => {
    const cartItemApiClient = new APIClient<UpdateQtyShape>(`/cart/${cartId}/items/${itemId}/`) // Item-specific endpoint
    mutation.mutate({ itemId, newQuantity, apiClient: cartItemApiClient })
  }

  const mutation = useMutation<UpdateQtyShape, AxiosError<ErrorResponse>, { itemId: number, newQuantity: number, apiClient: APIClient<UpdateQtyShape> }>({
    mutationFn: ({ newQuantity, apiClient }) => {
      // Using the specific APIClient instance for this item
      return apiClient.patch({ quantity: newQuantity })
    },

    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CACHE_KEY_CART_ITEMS]
      })
    },
  })

  return { mutation, handleQuantityUpdate }
}
export default useUpdateCart
