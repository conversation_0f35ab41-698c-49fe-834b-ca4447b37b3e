import { useEffect } from "react"
import { Link, useSearchParams } from "react-router-dom"
import useLocalStorageState from "use-local-storage-state"
import OrdersListView from "../../components/orders-list/orders-card/OrdersListView"
import OrdersTableView from "../../components/orders-list/orders-table/OrdersTableView"
import Alert from "../../components/utils/alert/Alert"
import Pagination from "../../components/utils/pagination/Pagination"
import Spinner from "../../components/utils/spinner/Spinner"
import { ITEMS_PER_PAGE } from "../../react-query/hooks/constants"
import useDeleteOrder from "../../react-query/hooks/order-hooks/useDeleteOrder"
import useGetAllOrders from "../../react-query/hooks/order-hooks/useGetAllOrders"
import styles from "./OrderList.module.scss"
import ViewToggle from "../../components/orders-list/toggle-view/ViewToggle"


const OrderList = () => {
  const [viewMode, setViewMode] = useLocalStorageState<'list' | 'table'>("table")
  const [activeIcon, setActiveIcon] = useLocalStorageState<'list' | 'table'>("table")
  const [searchParams, setSearchParams] = useSearchParams()
  const page = parseInt(searchParams.get("page") || "1", 10)

  const { isPending, error, data: orders } = useGetAllOrders(page, searchParams.toString())
  const { deleteOrder } = useDeleteOrder()

  useEffect(() => {
    setActiveIcon(viewMode)
  }, [viewMode, setActiveIcon])

  const toggleView = (mode: 'list' | 'table') => {
    setViewMode(mode)
  }

  const handlePageChange = (newPage: number) => {
    setSearchParams({ ...Object.fromEntries(searchParams), page: newPage.toString() })
  }

  return (
    <>
      {isPending ? (
        <Spinner color="#0091CF" size={20} loading={true} bgOpacity={0} />
      ) : error ? (
        <Alert variant="error" message={error.message} />
      ) : orders?.results.length === 0 ? (
        <div className={`container ${styles.no_orders}`}>
          <p>You have not placed any orders yet. </p>
          <Link to="/">Continue Shopping</Link>
        </div>
      ) : (
        <>
          <h3 className={styles.title}>Order List</h3>
          {deleteOrder.isError && (
            <>
              <Alert variant="error" message="Error deleting order" />
              <Alert variant="info" message="You can delete an order if it is in PENDING State"></Alert>
            </>
          )}
          <div className="container">
            <ViewToggle
              activeIcon={activeIcon ?? "table"}
              onToggle={toggleView}
            />
            {viewMode === "list" ? (
              <OrdersListView orders={orders.results} />
            ) : (
              <OrdersTableView orders={orders.results} />
            )}
            {/* Add Pagination */}
            {orders && orders.count > 0 && (
              <Pagination
                currentPage={page}
                totalPages={Math.ceil(orders.count / ITEMS_PER_PAGE)} // Assuming 10 items per page
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </>
      )}
    </>
  )
}

export default OrderList
