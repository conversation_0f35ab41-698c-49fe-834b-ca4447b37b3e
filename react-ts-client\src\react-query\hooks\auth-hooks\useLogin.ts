import { useMutation } from '@tanstack/react-query'
import { z } from "zod"
import authStore from "../../../zustand/authStore"
import APIClient from "../../services/auth-client"
import { loginSchema } from "../../../types/schemas"


type LoginUserShape = z.infer<typeof loginSchema>

const useLogin = () => {
  const { setIsLoggedIn } = authStore()
  const apiClient = new APIClient(`/users/login/`)

  const mutation = useMutation({
    mutationFn: (data: LoginUserShape) => apiClient.post(data),
    onSuccess: () => { // data is the response data
      // const { access } = data.data
      setIsLoggedIn(true)
    }
    // onSettled: (data, error, variables) => {
    //   console.log(data)
    //   console.log(error)
    //   console.log(variables) // variables are user input data
    // }
  })

  return { mutation }
}

export default useLogin