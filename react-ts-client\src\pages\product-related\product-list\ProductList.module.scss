@import '../../../scss/variables';
@import '../../../scss/mixins';


.product_list__container {
  @include flexbox(center, center, column);
}

.mobile_controls {
  background-color: $lighten-blue;
  @include flexbox(space-between, center);

  button {
    @include flexbox(flex-start, center);
    gap: 0.5rem;
    padding: $padding-2 $padding-4;
    background: none;
    border: none;
    cursor: pointer;
    color: $primary-dark-text-color;
    font-weight: bold;

    span {
      background-color: $sky-lighter-blue;
      padding: 0 5px;
      border-radius: 3px;
      color: $primary-dark-text-color;
    }
  }
}

.filters {
  display: none;

  &.show {
    display: block;
  }
}

.product_list__wrapper {
  width: 100%;
  flex: 1;
}

.sorting {
  display: none;
  margin: .5rem 1rem 1rem 0;

  &.show {
    display: block;
  }
}

.product_list {
  margin: 1rem 0 0 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  list-style-type: none;
  padding: 0;
  overflow: hidden;
  justify-items: center;

  li {
    width: 100%;
  }
}

.empty_products {
  width: 100%;
  grid-column: 1 / -1;
  text-align: center;
  font-style: italic;
  font-size: $font-size-4;
  color: $primary-lighter-text-color;
}

@media (width > $laptop) {
  .product_list__container {
    display: grid;
    grid-template-columns: auto 80%;
  }

  .product_list__wrapper {
    .product_list {
      padding: 0 1rem;
      justify-items: start;
    }
  }

  .mobile_controls {
    display: none;
  }

  .filters {
    display: block;
    width: 100%;
  }

  .sorting {
    display: block;
    text-align: right;
  }
}