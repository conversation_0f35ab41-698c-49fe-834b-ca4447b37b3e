import { useMutation } from '@tanstack/react-query'
import { useNavigate } from "react-router-dom"
import cartStore from "../../../zustand/cartStore"
import orderStore from "../../../zustand/orderStore"
import APIClient from "../../services/api-client"
import { OrderShape, createOrderShape } from '../../../types/order-types'

const useCreateOrder = () => {
  const navigate = useNavigate()
  const { setOrderId } = orderStore()
  const { setCartId } = cartStore()

  const apiClient = new APIClient<OrderShape, createOrderShape>(`/orders/`)

  const createOrder = useMutation<OrderShape, Error, createOrderShape>({
    mutationFn: (data) => apiClient.post(data),
    onSuccess: (data) => {
      console.log(data)
      setOrderId(data.id)
      setCartId(null)
      localStorage.removeItem('cart_store')
      if (data.id) {
        navigate(`/checkout/order/${data.id}/`)
      }
    },
  })

  return { createOrder }
}

export default useCreateOrder
