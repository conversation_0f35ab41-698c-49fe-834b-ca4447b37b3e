import { useQuery } from '@tanstack/react-query'
import APIClient from '../../services/api-client'
import useOrder from '../order-hooks/useOrder'

interface ClientSecretShape {
  client_secret: string
}

const useClientSecret = (orderId: number) => {
  const { data: order } = useOrder(orderId)
  const apiClient = new APIClient<ClientSecretShape>(`payments/payment-intent-secret/`)

  return useQuery({
    queryKey: ['stripeClientSecret', orderId],
    queryFn: () => apiClient.get({
      params: {
        order_id: orderId
      }
    }),
    // If order is not null or undefined and payment method is Stripe, then enable the query
    enabled: !!order && order.payment_method.slug === 'stripe'
  })

}

export default useClientSecret
