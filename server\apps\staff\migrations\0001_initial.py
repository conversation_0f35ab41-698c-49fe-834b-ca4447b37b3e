# Generated by Django 5.1.6 on 2025-06-29 14:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='APIAccessLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(db_index=True, max_length=255)),
                ('method', models.CharField(max_length=10)),
                ('status_code', models.IntegerField()),
                ('ip_address', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('response_time', models.FloatField(help_text='Response time in seconds')),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'API Access Log',
                'verbose_name_plural': 'API Access Logs',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='staff_apiac_user_id_4672c9_idx'), models.Index(fields=['endpoint', 'timestamp'], name='staff_apiac_endpoin_54916c_idx'), models.Index(fields=['status_code', 'timestamp'], name='staff_apiac_status__7d8001_idx')],
            },
        ),
        migrations.CreateModel(
            name='GroupMembership',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('assigned_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about this group assignment')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_staff_memberships', to=settings.AUTH_USER_MODEL)),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_memberships', to='auth.group')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_group_memberships', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Group Membership',
                'verbose_name_plural': 'Group Memberships',
                'indexes': [models.Index(fields=['user', 'is_active'], name='staff_group_user_id_2b8aec_idx'), models.Index(fields=['group', 'is_active'], name='staff_group_group_i_6c86ed_idx')],
                'unique_together': {('user', 'group')},
            },
        ),
        migrations.CreateModel(
            name='PermissionAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('group_created', 'Group Created'), ('group_updated', 'Group Updated'), ('group_deleted', 'Group Deleted'), ('user_added_to_group', 'User Added to Group'), ('user_removed_from_group', 'User Removed from Group'), ('permission_granted', 'Permission Granted'), ('permission_revoked', 'Permission Revoked'), ('user_staff_toggled', 'User Staff Status Toggled')], db_index=True, max_length=50)),
                ('details', models.JSONField(default=dict, help_text='Additional details about the action')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('performed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('target_group', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='auth.group')),
                ('target_user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='staff_permission_audits', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Permission Audit',
                'verbose_name_plural': 'Permission Audits',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['action', 'timestamp'], name='staff_permi_action_cfee2f_idx'), models.Index(fields=['performed_by', 'timestamp'], name='staff_permi_perform_4af82b_idx')],
            },
        ),
    ]
