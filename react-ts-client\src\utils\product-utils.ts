import { ProductShape, ProductVariant } from "../types/product-types"

/**
 * Finds the first available image URL across all product variants
 * @param product The product object containing variants
 * @param cloudinaryUrl The base URL for Cloudinary images
 * @returns The full image URL or null if no images are found
 */
export const findFirstAvailableImage = (
  product: ProductShape | undefined,
  cloudinaryUrl: string
): string | null => {
  if (!product || !product.product_variant || product.product_variant.length === 0) {
    return null
  }

  // First, try to find an image in any variant
  for (const variant of product.product_variant) {
    if (variant.product_image && variant.product_image.length > 0) {
      return `${cloudinaryUrl}/${variant.product_image[0].image}`
    }
  }

  // If no images found in any variant
  return null
}

/**
 * Finds the first available image URL for a specific variant, or falls back to other variants
 * @param variant The primary variant to check for images
 * @param product The full product object (for fallback to other variants)
 * @param cloudinaryUrl The base URL for Cloudinary images
 * @returns The full image URL or null if no images are found
 */
export const findVariantImage = (
  variant: ProductVariant | undefined,
  product: ProductShape | undefined,
  cloudinaryUrl: string
): string | null => {
  // First check if the specified variant has images
  if (variant?.product_image && variant.product_image.length > 0) {
    return `${cloudinaryUrl}/${variant.product_image[0].image}`
  }

  // If not, fall back to any image from any variant
  return findFirstAvailableImage(product, cloudinaryUrl)
}
