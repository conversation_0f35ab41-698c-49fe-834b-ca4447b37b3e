import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../../services/auth-client"
import { VerifyDetailShape } from '../../../../../pages/auth/resetting-forgotten-password/VerifyResetCode'


const useResetPasswordConfirm = () => {

  const apiClient = new APIClient(`/password-reset-confirm/`)

  const mutation = useMutation({
    mutationFn: (data: VerifyDetailShape) => apiClient.post(data)
  })

  return { mutation }
}

export default useResetPasswordConfirm