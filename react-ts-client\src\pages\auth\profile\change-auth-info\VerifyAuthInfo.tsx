import { useForm, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { verificationCodeSchema } from '../../../../types/schemas'
import useSendVerifyCode from '../../../../react-query/hooks/auth-hooks/update-auth-info/useSendVerifyCode'
import Logo from '../../../../components/utils/logo/Logo'
import Alert from '../../../../components/utils/alert/Alert'
import loading_svg from '../../../../assets/loading_svg_blue.svg'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import styles from './ChangeAuthInfo.module.scss'
import { useNavigate } from 'react-router-dom'

export type VerifyCodeShape = z.infer<typeof verificationCodeSchema>

const VerifyAuthInfo = () => {
  const navigate = useNavigate()
  const { register, handleSubmit, formState: { errors } } = useForm<VerifyCodeShape>({
    resolver: zodResolver(verificationCodeSchema),
  })

  const { mutation } = useSendVerifyCode()

  const onSubmit: SubmitHandler<VerifyCodeShape> = async (data) => {
    mutation.mutate(data, {
      onSuccess: () => {
        navigate('/customer')
      },
    })
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        {/* <h2 className='title'>Verify Contact Information</h2> */}

        {mutation.error && (
          <Alert
            variant="error"
            message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)}
            textAlign='center'
          />
        )}

        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <Alert
            variant="success"
            message={`Enter the received verification code here.`}
          // textAlign='center'
          />

          <div className='form_group'>
            <label className='form_label'>Enter the verification code:</label>
            {/* <input
              className='form_input'
              type='number'
              {...register("code")}
              disabled={mutation.isPending}
            /> */}
            <input
              className='form_input'
              type='text'
              inputMode='numeric'
              id='verification_code'
              disabled={mutation.isPending}
              onInput={(e) => {
                e.currentTarget.value = e.currentTarget.value.replace(/[^0-9]/g, '')
              }}
              {...register("code")}
            // style={{ appearance: 'textfield', MozAppearance: 'textfield' }}
            />
            {errors.code && (
              <p className='form_error'>{errors.code.message}</p>
            )}
          </div>

          <button className={`empty_btn ${styles.empty_btn_2}`} type="submit" disabled={mutation.isPending}>
            {mutation.isPending ? (
              <img src={loading_svg} alt="Loading..." className='loading_svg' />
            ) : 'Submit'}
          </button>
        </form>
      </div>
    </div>
  )
}

export default VerifyAuthInfo