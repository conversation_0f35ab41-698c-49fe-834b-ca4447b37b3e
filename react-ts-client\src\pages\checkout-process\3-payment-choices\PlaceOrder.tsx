import { useEffect } from "react"
import { Link, useNavigate } from "react-router-dom"
import loading_svg from '../../../assets/loading_svg_white.svg'
import CartItemsList from "../../../components/checkout/cart/CartItemsList"
import PriceSummary from "../../../components/checkout/price-summary/PriceSummary"
import Alert from "../../../components/utils/alert/Alert"
import EmptyCart from "../../../components/utils/empty-cart/EmptyCart"
import Logo from "../../../components/utils/logo/Logo"
import Spinner from "../../../components/utils/spinner/Spinner"
import useCart from "../../../react-query/hooks/cart-hooks/useCart"
import usePaymentOptions from '../../../react-query/hooks/checkout-hooks/usePaymentOptions'
import useCustomerDetails from "../../../react-query/hooks/customer-related-hooks/useCustomerDetails"
import useCreateOrder from "../../../react-query/hooks/order-hooks/useCreateOrder"
import { PaymentOptionsShape } from '../../../types/types'
import authStore from "../../../zustand/authStore"
import cartStore from "../../../zustand/cartStore"
import styles from './PlaceOrder.module.scss'


const PlaceOrder = () => {
  const navigate = useNavigate()
  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { cartId, setSelectedPaymentOption, selectedAddress, selectedPaymentOption } = cartStore()
  const { isPending, error, data } = useCart()

  const { createOrder } = useCreateOrder()
  const payOptions = usePaymentOptions()

  console.log(payOptions.data)

  useEffect(() => {
    if (!isLoggedIn) {
      navigate('/user/login')
    }
  }, [isLoggedIn, navigate])

  const handlePaymentOptionChange = (paymentOption: PaymentOptionsShape) => {
    setSelectedPaymentOption(paymentOption)
  }

  useEffect(() => {
    if (payOptions.data && payOptions.data.length > 0 && !selectedPaymentOption) {
      setSelectedPaymentOption(payOptions.data[0])
    }
  }, [payOptions.data, selectedPaymentOption, setSelectedPaymentOption])

  console.log(customer)
  console.log(selectedAddress)
  console.log(selectedPaymentOption)

  const createOrderFn = () => {
    if (window.confirm("Payment Method or other order details cannot be changed after placing the order.\n" +
      "Make sure you have selected the correct payment method and other order details before placing the order.")) {
      if (customer?.id && selectedAddress?.id && selectedPaymentOption?.id) {
        createOrder.mutate({
          cart_id: cartId!,
          delivery_status: "Pending",
          selected_address: selectedAddress.id,
          payment_method: selectedPaymentOption.id
        })
      }
    }
  }

  return (
    <>
      {!cartId ? <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' /> :
        isPending ? <Spinner color='#0091CF' size={20} loading={true} /> :
          error ? <Alert variant="error" message={error.message} /> :
            (!data || data?.cart_items?.length === 0 || Object.keys(data).length === 0) ? (
              <div className={styles.empty_cart}>
                <p>Your cart is empty. Add some products to the cart to checkout!</p>
                <Link to='/'>Go Shopping </Link>
              </div>
            ) : (
              <>
                <div className={styles.logo}>
                  <Logo />
                </div>
                <div className='container'>
                  <h3 className={styles.place_order}>Place Order</h3>
                  <div className={styles.payment_options_stage}>
                    <section>
                      <section className={styles.contact_info}>
                        <div className={styles.contact_details}>
                          <h3>Contact Details: </h3>
                          <p>Deliver to: {customer?.first_name} {customer?.last_name}</p>
                          <p>Phone: {customer?.phone_number}</p>
                          <p>Email to: {customer?.email}</p>
                        </div>
                        <div className={styles.shipping_address}>
                          <h3>Shipping address: </h3>
                          <address>
                            {selectedAddress?.full_name},<br />
                            {selectedAddress?.street_name},<br />
                            {selectedAddress?.postal_code},<br />
                            {selectedAddress?.city_or_village}<br />
                          </address>
                        </div >
                      </section>
                      <hr />
                      <div className={styles.cart}>
                        <CartItemsList
                          cartItems={data.cart_items}
                        />
                      </div>
                      <hr />
                      <section>
                        <div className={styles.payment_options}>
                          <h3>Payment Method:</h3>
                          {payOptions?.isPending ? <Alert variant="info" message="Payment options are loading" /> :
                            payOptions?.error ? <Alert variant="error" message={payOptions.error.message} /> :
                              payOptions?.data?.length === 0 ? <Alert variant="error" message="No payment options available" /> :
                                <>
                                  {payOptions?.data?.map((option: PaymentOptionsShape) => (
                                    <div key={option.id}>
                                      <input
                                        type="radio"
                                        id={`payment-${option.id}`}
                                        name="payment-option"
                                        checked={selectedPaymentOption?.id === option.id}
                                        onChange={() => handlePaymentOptionChange(option)}
                                      />
                                      <label htmlFor={`payment-${option.id}`}>{option.name}</label>
                                    </div>
                                  ))}
                                </>}
                        </div>
                      </section>
                    </section >
                    <section className={styles.price_summary}>
                      <PriceSummary
                        totalPrice={data?.total_price}
                        shippingCost={data?.shipping_cost}
                        grandTotal={data?.grand_total}
                        cart_weight={data?.cart_weight}
                      />
                      <button type="submit" disabled={createOrder.isPending} onClick={createOrderFn}>
                        {createOrder.isPending ? (
                          <img src={loading_svg} alt="Loading..." className='loading_svg' />
                        ) : (
                          'Place Order'
                        )}
                      </button>
                    </section>
                  </div>
                </div>
              </>
            )}
    </ >
  )
}


export default PlaceOrder