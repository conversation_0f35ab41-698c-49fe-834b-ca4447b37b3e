import { useEffect, useState } from "react"
import { useNavigate, useParams } from "react-router-dom"
import useAccountActivation from "../../../react-query/hooks/auth-hooks/useAccountActivation"
import Alert from "../../../components/utils/alert/Alert"
import { getErrorMessage } from "../../../components/utils/getErrorMessage"
import { AxiosError } from "axios"
import { ErrorResponse } from "../../../types/types"
import Logo from "../../../components/utils/logo/Logo"
import Underlay from "../../../components/utils/underlay/Underlay"
import styles from './Activation.module.scss'

const Activation = () => {
  const navigate = useNavigate()
  const { uid, token } = useParams<{ uid: string, token: string }>()
  const { mutation } = useAccountActivation()
  const [activationAttempted, setActivationAttempted] = useState(false)

  useEffect(() => {
    if (uid && token && !activationAttempted) {
      mutation.mutate({ uid, token })
      setActivationAttempted(true)
    }
  }, [])

  return (
    <Underlay isOpen>
      <div className={styles.activation}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
        {mutation.isSuccess &&
          <>
            <p className='success_message'>Account activated successfully.</p>
            <section className="btn_container">
              <button className="empty_btn" onClick={() => navigate('/user/login/')}>Login</button>
            </section>
          </>
        }
      </div>
    </Underlay>
  )
}

export default Activation