import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import Logo from '../../../../components/utils/logo/Logo'
import loading_blue from '../../../../assets/loading_svg_blue.svg'
import Alert from '../../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import { changeAuthInfoSchema } from '../../../../types/schemas'
import { useNavigate } from 'react-router-dom'
import useChangeAuthInfo from '../../../../react-query/hooks/auth-hooks/change-auth-info/useChangeAuthInfo'
import styles from './ChangeAuthInfo.module.scss'
import { NewAuthInfoShape } from './ChangePhoneNumber'
import Modal from '../../../../components/utils/modal/Modal'
import useCustomerDetails from '../../../../react-query/hooks/customer-related-hooks/useCustomerDetails'
import authStore from "../../../../zustand/authStore"

const ChangeEmail = () => {
  const navigate = useNavigate()
  const { mutation } = useChangeAuthInfo()
  const { isLoggedIn } = authStore()
  const { data: customerData } = useCustomerDetails(isLoggedIn)

  // Initialize the form with default values from customerData
  const { register, handleSubmit, formState: { errors }, setValue } = useForm<NewAuthInfoShape>({
    resolver: zodResolver(changeAuthInfoSchema),
    defaultValues: {
      email: customerData?.email || '',
    }
  })

  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState<NewAuthInfoShape | null>(null)

  useEffect(() => {
    // Update the email field when customerData is loaded
    if (customerData?.email) {
      setValue('email', customerData.email)
    }
  }, [customerData, setValue])

  const onSubmit: SubmitHandler<NewAuthInfoShape> = (data) => {
    setFormData(data) // Save form data temporarily
    setShowModal(true) // Open the modal
  }

  const handleConfirm = () => {
    if (formData) {
      mutation.mutate(formData, {
        onSuccess: () => {
          navigate('/user/submit-auth-info-verify-code')
        }
      })
    }
    setShowModal(false) // Close the modal
  }

  const handleCancel = () => {
    setShowModal(false) // Close the modal
  }

  return (
    <div className={styles.register_container}>
      <div className={styles.form_container}>
        <div className='logo_header'>
          <Logo />
        </div>
        <h2 className='title'>Change email</h2>
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
          <div className='form_group'>
            <label className='form_label' htmlFor="email">Enter a new email:</label>
            <input
              className='form_input'
              type="email"
              id="email"
              {...register("email")}
            />
            {errors.email && <p>{errors.email.message} &#128543;</p>}
          </div>
          <section className='btn_container'>
            <button
              type="button"
              className='empty_btn'
              disabled={mutation.isPending}
              onClick={() => navigate('/customer')}>Cancel
            </button>
            <button type="submit" className='empty_btn' disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_blue} alt="Loading..." className='loading_svg' />
              ) : (
                'Update'
              )}
            </button>
          </section>
        </form>

        {/* Modal for confirmation */}
        <Modal
          title="Confirm Email Change"
          message="Are you sure you want to change your email?"
          show={showModal}
          onClose={handleCancel}
          onConfirm={handleConfirm}
          btn1="Yes"
          btn2="No"
        />
      </div>
    </div>
  )
}

export default ChangeEmail
