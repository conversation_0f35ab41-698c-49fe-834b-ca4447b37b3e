import { useMutation } from '@tanstack/react-query'
import APIClient from '../../../services/auth-client'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'


type UpdateAuthInfoResponseShape = {
  message: string
  username: string
}

export interface UpdateAuthInfoInitShape {
  email?: string
  phone_number?: string
}

const useSendUpdateAuthInfo = () => {
  const apiClient = new APIClient<UpdateAuthInfoResponseShape, UpdateAuthInfoInitShape>('add-contact/initiate/')
  // const { setAltUsername } = authStore()

  const authInfoMutation = useMutation<UpdateAuthInfoResponseShape, AxiosError<ErrorResponse>, UpdateAuthInfoInitShape>({
    mutationFn: (data) => apiClient.patch(data),
    // onSuccess: (data) => {
    //   console.log(data)
    //   setAltUsername(data.username)
    // }
  })

  return { authInfoMutation }
}

export default useSendUpdateAuthInfo
