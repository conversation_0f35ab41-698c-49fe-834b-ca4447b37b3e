import { useMutation } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import AuthClient from '../../../services/auth-client'
import { ErrorResponse } from '../../../../types/types'
import authStore from '../../../../zustand/authStore'
import { InitRegUserShape } from '../../../../pages/auth/register/1-initiate-registration/InitiateRegistration'


type RegResponseShape = {
  message: string
  username: string
}

const useRegister = () => {
  const { setUsername } = authStore()

  // In AuthClient Response has defined as: Request = Response 
  // If request data is different do not forget to specify the types here. 
  const apiClient = new AuthClient<RegResponseShape, InitRegUserShape>(`/initiate-registration/`)

  const mutation = useMutation<RegResponseShape, AxiosError<ErrorResponse>, InitRegUserShape>({
    mutationFn: (data: InitRegUserShape) => apiClient.post(data),  // Here `data` is of type `RegisterUserShape`
    onSuccess: (data) => {
      console.log(data)
      setUsername(data.username)
    }
  })

  return { mutation }
}

export default useRegister
