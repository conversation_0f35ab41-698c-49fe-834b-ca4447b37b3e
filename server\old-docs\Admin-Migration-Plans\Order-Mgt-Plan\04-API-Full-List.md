# Staff Orders API - Full Endpoint List

## Base URL: `/api/staff/orders/`

---

## 1. StaffOrderViewSet - `/api/staff/orders/orders/`

### GET /api/staff/orders/orders/

**Description:** List all orders accessible to the current staff user (filtered by role)
**Request body:** None (GET request)

### POST /api/staff/orders/orders/

**Description:** Create a new order (if permissions allow)
**Request body:**

```json
{
  "customer": 1,
  "ordered_items": [
    {
      "product": 1,
      "product_variant": 1,
      "quantity": 2,
      "total_price": "199.98"
    }
  ],
  "selected_address": 1,
  "payment_method": 1,
  "shipping_cost": "9.99"
}
```

### GET /api/staff/orders/orders/{id}/

**Description:** Retrieve detailed information about a specific order
**Request body:** None (GET request)

### PUT /api/staff/orders/orders/{id}/

**Description:** Update an entire order
**Request body:**

```json
{
  "customer": 1,
  "payment_status": "Paid",
  "delivery_status": "Processing",
  "ordered_items": [
    {
      "product": 1,
      "product_variant": 1,
      "quantity": 2,
      "total_price": "199.98"
    }
  ],
  "selected_address": 1,
  "payment_method": 1,
  "shipping_cost": "9.99"
}
```

### PATCH /api/staff/orders/orders/{id}/

**Description:** Partially update an order
**Request body:**

```json
{
  "delivery_status": "Dispatched"
}
```

### DELETE /api/staff/orders/orders/{id}/

**Description:** Delete an order (if permissions allow)
**Request body:** None (DELETE request)

### PATCH /api/staff/orders/orders/{id}/update_status/

**Description:** Update order delivery status with audit trail
**Request body:**

```json
{
  "delivery_status": "Processing",
  "notes": "Order is being prepared for shipment"
}
```

### POST /api/staff/orders/orders/{id}/assign/

**Description:** Assign order to a staff member
**Request body:**

```json
{
  "assigned_to_id": 5,
  "notes": "Assigned for priority processing"
}
```

### POST /api/staff/orders/orders/{id}/add_note/

**Description:** Add a note to the order
**Request body:**

```json
{
  "note": "Customer requested expedited shipping",
  "is_internal": true
}
```

### GET /api/staff/orders/orders/dashboard_stats/

**Description:** Get dashboard statistics for orders
**Request body:** None (GET request)

### GET /api/staff/orders/orders/my_assignments/

**Description:** Get orders assigned to the current staff member
**Request body:** None (GET request)

---

## 2. OrderStatusHistoryViewSet - `/api/staff/orders/status-history/`

### GET /api/staff/orders/status-history/

**Description:** List order status history records accessible to the current staff user
**Request body:** None (GET request)

### GET /api/staff/orders/status-history/{id}/

**Description:** Retrieve detailed information about a specific status history record
**Request body:** None (GET request)

---

## 3. OrderAssignmentViewSet - `/api/staff/orders/assignments/`

### GET /api/staff/orders/assignments/

**Description:** List all order assignments accessible to the current staff user
**Request body:** None (GET request)

### POST /api/staff/orders/assignments/

**Description:** Create a new order assignment
**Request body:**

```json
{
  "order": 1,
  "assigned_to": 5,
  "notes": "High priority order assignment"
}
```

### GET /api/staff/orders/assignments/{id}/

**Description:** Retrieve detailed information about a specific assignment
**Request body:** None (GET request)

### PUT /api/staff/orders/assignments/{id}/

**Description:** Update an entire assignment
**Request body:**

```json
{
  "order": 1,
  "assigned_to": 5,
  "is_active": true,
  "notes": "Updated assignment notes"
}
```

### PATCH /api/staff/orders/assignments/{id}/

**Description:** Partially update an assignment
**Request body:**

```json
{
  "is_active": false,
  "notes": "Assignment completed"
}
```

### DELETE /api/staff/orders/assignments/{id}/

**Description:** Delete an assignment
**Request body:** None (DELETE request)

---

## 4. OrderNoteViewSet - `/api/staff/orders/notes/`

### GET /api/staff/orders/notes/

**Description:** List all order notes accessible to the current staff user
**Request body:** None (GET request)

### POST /api/staff/orders/notes/

**Description:** Create a new order note
**Request body:**

```json
{
  "order": 1,
  "note": "Customer called to confirm delivery address",
  "is_internal": false
}
```

### GET /api/staff/orders/notes/{id}/

**Description:** Retrieve detailed information about a specific note
**Request body:** None (GET request)

### PUT /api/staff/orders/notes/{id}/

**Description:** Update an entire note
**Request body:**

```json
{
  "order": 1,
  "note": "Updated note content",
  "is_internal": true
}
```

### PATCH /api/staff/orders/notes/{id}/

**Description:** Partially update a note
**Request body:**

```json
{
  "note": "Updated note content",
  "is_internal": false
}
```

### DELETE /api/staff/orders/notes/{id}/

**Description:** Delete a note
**Request body:** None (DELETE request)

---

## Permission Requirements

All endpoints require:

- **Authentication:** User must be authenticated
- **Staff Status:** User must have `is_staff=True`
- **Specific Permissions:** Based on user's group membership:
  - **Order Management Executive (OME):** Full access to all orders
  - **Order Fulfillment Specialist (OFS):** Access to Pending and Processing orders
  - **Order Management Group Member (OMGM):** Access to Pending, Processing, and Dispatched orders
  - **Customer Service:** Full access for support purposes

## Status Transition Rules

Order status transitions are validated:

- **Pending** → **Processing**
- **Processing** → **Dispatched**
- **Dispatched** → **Delivered**
- **Delivered** → No further transitions allowed

## Notes

- All list endpoints support pagination using `StaffPagination`
- Order assignments automatically deactivate previous assignments when creating new ones
- Status updates create audit trail entries in `OrderStatusHistory`
- Notes can be marked as internal (staff-only) or external (customer-visible)
- All create operations automatically set the current staff user as the creator/assignor
