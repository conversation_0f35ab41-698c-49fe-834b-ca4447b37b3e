// Zustand store (filterStore.ts)
import { create } from 'zustand'

interface FilterStoreShape {
  productTypeId: number
  selectedFilters: Record<string, string | number | (string | number)[] | [number, number]>
  setProductTypeId: (proType: number) => void
  updateFilter: (filterName: string, filterValue: string | number | (string | number)[]) => void
  resetFilters: () => void
}

const filterStore = create<FilterStoreShape>()((set) => ({
  productTypeId: 0,
  selectedFilters: {},
  setProductTypeId: (proType) => set({ productTypeId: proType }),
  updateFilter: (filterName, filterValue) =>
    set((state) => ({
      selectedFilters: { ...state.selectedFilters, [filterName]: filterValue },
    })),
  resetFilters: () => set({ productTypeId: 0, selectedFilters: {} }),
}))

export default filterStore
