import { useQuery } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { Category } from '../../../types/product-types'

const apiClient = new APIClient<Category[]>('/products/categories/')

const useCategories = () => useQuery({
  queryKey: ['categories'],
  queryFn: ({ signal }) => apiClient.get({ signal }), // ✅ Pass abort signal
  staleTime: 24 * 60 * 60 * 1000, // Revalidate data every 24 hours
  // initialData:  Here we can add categories as static data
  // cacheTime: 1000 * 60 * 60, <-- This is wrong!
  gcTime: 1000 * 60 * 60, // 1 hour
  retry: (failureCount, error) => {
    // ✅ Don't retry on cancelled requests
    if (error.name === 'CanceledError' || error.message === 'canceled') {
      return false
    }
    return failureCount < 3
  }
})

export default useCategories
