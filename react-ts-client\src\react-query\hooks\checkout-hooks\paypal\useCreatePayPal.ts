import { useMutation } from '@tanstack/react-query'
import APIClient from "../../../services/api-client"

interface PayPalOrderRequest {
  order_id: number
  amount: string
}

interface PayPalOrderResponse {
  id: string
  status: string
}

const useCreatePayPalOrder = ({ orderId, amount }: { orderId: number, amount: number }) => {
  const apiClient = new APIClient<PayPalOrderResponse, PayPalOrderRequest>('payments/create-paypal-order/')

  return useMutation({
    mutationFn: () => apiClient.post({
      order_id: orderId,
      amount: amount.toString()
    })
  })
}

export default useCreatePayPalOrder
