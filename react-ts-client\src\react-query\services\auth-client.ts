import axios, { AxiosError, AxiosRequestConfig } from "axios"

const axiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BACKEND_AUTH_URL,
  withCredentials: true, // Include credentials (cookies) with requests
})

class AuthClient<TResponse, TRequest = TResponse> {
  endpoint: string

  constructor(endpoint: string) {
    this.endpoint = endpoint
  }

  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.get<TResponse>(this.endpoint, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  post = async (data?: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    console.log("data: ", data)
    try {
      const response = await axiosInstance.post<TResponse>(this.endpoint, data, config)
      console.log('response', response)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  patch = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.patch<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  delete = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await axiosInstance.delete<TResponse>(this.endpoint, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw new Error((error as AxiosError).message)
    }
  }

  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      console.error("Error message: ", error.message)
      if (error.response) {
        console.error("Response data: ", error.response.data)
        console.error("Response status: ", error.response.status)
        console.error("Response headers: ", error.response.headers)
        throw {
          message: error.message,
          response: error.response,
        }
      } else if (error.request) {
        console.error("Request data: ", error.request)
      } else {
        console.error("Error config: ", error.config)
      }
    } else {
      console.error("Error: ", error)
    }
    throw error // Re-throw the error to be handled by the calling function if needed
  }
}

export default AuthClient
