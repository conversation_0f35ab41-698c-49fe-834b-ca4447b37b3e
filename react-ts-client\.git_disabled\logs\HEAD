0000000000000000000000000000000000000000 74b1e688aa7583a866a0ca502573ee4cbb5fe7cd Kanishka <<EMAIL>> 1718497754 +0530	commit (initial): first commit
74b1e688aa7583a866a0ca502573ee4cbb5fe7cd 0000000000000000000000000000000000000000 Kanishka <<EMAIL>> 1718497804 +0530	Branch: renamed refs/heads/main to refs/heads/main
74b1e688aa7583a866a0ca502573ee4cbb5fe7cd 74b1e688aa7583a866a0ca502573ee4cbb5fe7cd Kanishka <<EMAIL>> 1718497804 +0530	Branch: renamed refs/heads/main to refs/heads/main
74b1e688aa7583a866a0ca502573ee4cbb5fe7cd fb58ef12cc098d52fa05ec48ae7c6e7607c35376 Kanishka <<EMAIL>> 1725070722 +0530	commit: before adding primary filters
fb58ef12cc098d52fa05ec48ae7c6e7607c35376 6195b45986759565481c5e7136881dcf82bff9c7 Kanishka <<EMAIL>> 1728955460 +0530	commit: before change auth flow
6195b45986759565481c5e7136881dcf82bff9c7 5fa1de678d2dfa4fe7ce5be2a7e37ec8fc4b576e dev-kani <<EMAIL>> 1742347922 +0530	commit: before deploy to netlify
5fa1de678d2dfa4fe7ce5be2a7e37ec8fc4b576e a73999e543797d99dbc6e3b21e5fda3b56d1a5a3 dev-kani <<EMAIL>> 1742350138 +0530	commit: useOrder error fixed 1
a73999e543797d99dbc6e3b21e5fda3b56d1a5a3 86e1da6f2b974333b3fa61ccedfc3580dcccacd9 dev-kani <<EMAIL>> 1742350607 +0530	commit: useOrder error fixed 2
86e1da6f2b974333b3fa61ccedfc3580dcccacd9 a8fae64d014d8e9295dc928794e680424e19cdec dev-kani <<EMAIL>> 1742350851 +0530	commit: useOrder error fixed 3
a8fae64d014d8e9295dc928794e680424e19cdec fb4994fac48dd728b78517fc862b5451c0474e2d dev-kani <<EMAIL>> 1742494464 +0530	commit: Verification code validation error fixed
fb4994fac48dd728b78517fc862b5451c0474e2d 521fd45d42d2735dd0c962bb0a2c04219a409aa8 dev-kani <<EMAIL>> 1742495805 +0530	commit: Verification code validation error fixed in VerifyAuthContact component
521fd45d42d2735dd0c962bb0a2c04219a409aa8 185a7171b277e79d65752ea7ce2174dc30d1a71f dev-kani <<EMAIL>> 1747124859 +0530	commit: before apply attribute changes
185a7171b277e79d65752ea7ce2174dc30d1a71f eede7230d2997c3cc0a0532ccd7393c456c4035a dev-kani <<EMAIL>> 1747464954 +0530	commit: Added decimals to the price with toFixed
eede7230d2997c3cc0a0532ccd7393c456c4035a 6ac3218626dc80db7ac569a456c18ec99c6232fd dev-kani <<EMAIL>> 1747751696 +0530	commit: before adding attribute value filtering
6ac3218626dc80db7ac569a456c18ec99c6232fd 2c2d1d776b162fadf448360879c5cd31bb37205b dev-kani <<EMAIL>> 1748178963 +0530	commit: Reusable tooltip component added
2c2d1d776b162fadf448360879c5cd31bb37205b 56ac07ca068a5ed48b2668c9aaf4f40a00d91476 dev-kani <<EMAIL>> 1748246354 +0530	commit: Before adding attribute value validations when adding to the cart
56ac07ca068a5ed48b2668c9aaf4f40a00d91476 33ef0e07dc04621f9a9918b318c0ad9e3fe2aacf dev-kani <<EMAIL>> 1748359734 +0530	commit: before adding images instead of color attribute values
