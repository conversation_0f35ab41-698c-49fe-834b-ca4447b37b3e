# Generated by Django 5.1.6 on 2025-07-04 02:29

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0009_attributevalueproxy_producttypeattributeproxy'),
        ('staff', '0002_staffprofile_role_alter_groupmembership_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AttributeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Attribute',
                'verbose_name_plural': 'Staff Attributes',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attribute',),
        ),
        migrations.CreateModel(
            name='AttributeValueProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Attribute Value',
                'verbose_name_plural': 'Staff Attribute Values',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.attributevalue',),
        ),
        migrations.CreateModel(
            name='BrandProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Brand',
                'verbose_name_plural': 'Staff Brands',
                'permissions': [('manage_brand_product_types', 'Can manage brand product types')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.brand',),
        ),
        migrations.CreateModel(
            name='CategoryProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Category',
                'verbose_name_plural': 'Staff Categories',
                'permissions': [('move_category', 'Can move category in tree'), ('bulk_update_categories', 'Can bulk update categories')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.category',),
        ),
        migrations.CreateModel(
            name='ProductProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product',
                'verbose_name_plural': 'Staff Products',
                'permissions': [('bulk_update_products', 'Can bulk update products'), ('change_product_status', 'Can change product status'), ('manage_product_variants', 'Can manage product variants'), ('manage_product_images', 'Can manage product images')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.product',),
        ),
        migrations.CreateModel(
            name='ProductTypeProxy',
            fields=[
            ],
            options={
                'verbose_name': 'Staff Product Type',
                'verbose_name_plural': 'Staff Product Types',
                'permissions': [('manage_type_attributes', 'Can manage product type attributes')],
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('products.producttype',),
        ),
        migrations.CreateModel(
            name='BulkProductOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_id', models.UUIDField(editable=False, unique=True)),
                ('operation_type', models.CharField(choices=[('BULK_CREATE', 'Bulk Create'), ('BULK_UPDATE', 'Bulk Update'), ('BULK_DELETE', 'Bulk Delete'), ('BULK_STATUS_CHANGE', 'Bulk Status Change'), ('BULK_CATEGORY_ASSIGN', 'Bulk Category Assignment'), ('BULK_ATTRIBUTE_ASSIGN', 'Bulk Attribute Assignment')], max_length=30)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20)),
                ('total_items', models.PositiveIntegerField()),
                ('processed_items', models.PositiveIntegerField(default=0)),
                ('failed_items', models.PositiveIntegerField(default=0)),
                ('operation_data', models.JSONField(help_text='Operation parameters and data')),
                ('results', models.JSONField(default=dict, help_text='Operation results and errors')),
                ('started_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('error_message', models.TextField(blank=True)),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='bulk_operations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-started_at'],
                'indexes': [models.Index(fields=['staff_user', '-started_at'], name='staff_bulkp_staff_u_d4bb3f_idx'), models.Index(fields=['status', '-started_at'], name='staff_bulkp_status_48ddda_idx'), models.Index(fields=['operation_type', '-started_at'], name='staff_bulkp_operati_9c046a_idx')],
            },
        ),
        migrations.CreateModel(
            name='ProductAudit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('CREATE', 'Created'), ('UPDATE', 'Updated'), ('DELETE', 'Deleted'), ('BULK_UPDATE', 'Bulk Updated'), ('STATUS_CHANGE', 'Status Changed'), ('VARIANT_ADD', 'Variant Added'), ('VARIANT_UPDATE', 'Variant Updated'), ('VARIANT_DELETE', 'Variant Deleted'), ('IMAGE_ADD', 'Image Added'), ('IMAGE_UPDATE', 'Image Updated'), ('IMAGE_DELETE', 'Image Deleted')], max_length=20)),
                ('changes', models.JSONField(default=dict, help_text='JSON of field changes')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='audit_logs', to='products.product')),
                ('staff_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['product', '-timestamp'], name='staff_produ_product_b9340e_idx'), models.Index(fields=['staff_user', '-timestamp'], name='staff_produ_staff_u_9742c3_idx'), models.Index(fields=['action', '-timestamp'], name='staff_produ_action_6911d8_idx')],
            },
        ),
    ]
