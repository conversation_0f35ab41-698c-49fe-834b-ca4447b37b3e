import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { VerifyAuthContact } from './VerifyAuthContact'
import authStore from '../../../../zustand/authStore'
import UpdateAuthContact from './UpdateAuthContact'

export const UpdateAuthInfoContainer = () => {
  const navigate = useNavigate()
  const [verificationStep, setVerificationStep] = useState(false)
  const [altUsername, setAltUsername] = useState<string | null>(null)

  const {
    username,
    // setIsLoggedIn: login,
    setRegInitiated,
    setVerificationCodeSubmitted,
    setPasswordSubmitted,
    setUpdateAuthInfoSubmitted,
    setCustomerDetailsSubmitted,
  } = authStore()

  const handleContactInfoSuccess = (username: string) => {
    setAltUsername(username)
    setVerificationStep(true)
  }


  const handleVerificationSuccess = () => {
    // login()
    setPasswordSubmitted(false)
    setRegInitiated(false)
    setVerificationCodeSubmitted(false)
    setCustomerDetailsSubmitted(false)
    setUpdateAuthInfoSubmitted(false)
    navigate('/customer')
  }

  const handleSkip = () => {
    navigate('/customer')
  }

  return (
    <div>
      {!verificationStep ? (
        <UpdateAuthContact
          username={username}
          onSuccess={handleContactInfoSuccess}
          onSkip={handleSkip}
        />
      ) : (
        <VerifyAuthContact
          altUsername={altUsername!}
          onSuccess={handleVerificationSuccess}
        />
      )}
    </div>
  )
}