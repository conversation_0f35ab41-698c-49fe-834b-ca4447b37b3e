@import '../../../../scss/variables';
@import '../../../../scss/mixins';

.product_details {
  padding: 1rem .5rem;

  @media (width > 375px) {
    padding: 0;
  }
}

.product_title {
  font-size: $font-size-5;
  font-weight: 600;
  color: $primary-dark-text-color;
}

.rating_container {
  @include flexbox(flex-start, center);
  gap: 0.5rem;
}

.price_container {
  padding: $padding-1 0;
  position: relative;
}

.discount_badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: $primary-red;
  color: white;
  padding: $padding-1;
  border-radius: $border-radius-1;
  font-size: $font-size-1;
  font-weight: bold;
}

.price {
  @include flexbox(flex-start, baseline);
  gap: 0.5rem;
}

.current_price {
  font-size: $font-size-5;
  color: $primary-red;
  font-weight: bold;
}

.original_price {
  font-size: $font-size-2;
  text-decoration: line-through;
  color: $primary-lighter-text-color;
}

.savings {
  font-size: $font-size-1;
  color: $primary-lighter-text-color;
  margin-top: 0.25rem;
}

.divider {
  border: none;
  height: 1px;
  background-color: #e7e7e7;
  margin: 0.5rem 0;
}

.option_selector {
  margin: 0.5rem 0;
}

.selector_values {
  @include flexbox(flex-start, center, row, wrap);
  gap: 0.5rem;
  margin-top: 0.25rem;
  padding: 0.25rem 0;
}

.selector_value {
  padding: $padding-1 $padding-2;
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-2;
  cursor: pointer;
  min-width: 40px;
  text-align: center;
  transition: all 0.2s ease-in-out;

  // Special styling for color image buttons
  &:has(.color_image_button) {
    padding: 0;
    border: none;
    background: transparent;
    min-width: auto;
  }

  &:hover:not(:disabled) {
    background-color: #f7fafa;
    border-color: $primary-blue;
  }

  &.selected_value {
    border: 2px solid $primary-blue;
  }

  &.unavailable_value {
    opacity: 0.5;
    cursor: not-allowed;
    text-decoration: line-through;

    &:hover {
      border-color: #d5d9d9;
      background-color: #fff;
    }

    &:has(.color_image_button) {
      opacity: 1;
      text-decoration: none;

      &:hover {
        border-color: transparent;
        background-color: transparent;
      }
    }
  }

  &:disabled {
    cursor: not-allowed;
  }
}

.color_swatch {
  width: 25px;
  height: 25px;
  border-radius: 50%;
  border: 1px solid #d5d9d9;
  transition: all 0.2s ease;

  &:hover {
    transform: scale(1.1);
  }
}

.color_image_button {
  width: 60px;
  height: 60px;
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-2;
  overflow: hidden;
  cursor: pointer;
  position: relative;
  background: #fff;
  transition: all 0.1s ease-in-out;

  &:hover:not(:disabled) {
    scale: 1.02;
  }

  // &.selected_color_image {
  //   // border: 2px solid $primary-blue;
  // }

  &.unavailable_color_image {
    opacity: 0.45;
    cursor: not-allowed;
  }

  &:disabled {
    cursor: not-allowed;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.product_variants {
  padding: $padding-1 0;
  margin-top: 0.25rem;
}

.product_variants h3 {
  text-transform: capitalize;
  font-size: $font-size-2;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: $primary-dark-text-color;
}

.selector_title {
  font-size: $font-size-2;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: $primary-dark-text-color;
}

// Selected Attribute Value 
.selected_value_display {
  margin: 0 0 0 0.3rem;
  color: $primary-blue;
}

.missing_attr_msg {
  margin: 0 0 0 0.3rem;
  color: $primary-red;
}

.variants {
  margin: 0.25rem 0;
  @include flexbox(flex-start, center);
  flex-wrap: wrap;
  gap: 0.5rem;

  &:hover {
    cursor: pointer;
  }
}

.variant {
  padding: $padding-1 $padding-3;
  @include flexbox(center, center, column);
  border: 1px solid #d5d9d9;
  border-radius: $border-radius-2;

  &:hover {
    background-color: #f7fafa;
    border-color: $primary-blue;
  }
}

.variant__highlight {
  border: 2px solid $primary-blue;

  p:nth-child(2) {
    font-weight: bold;
  }
}

.stock_status {
  margin: 0.5rem 0;
}

.stock_indicator {
  font-weight: bold;
  padding: $padding-1 0;
}

.in_stock {
  color: $primary-green;
}

.low_stock {
  color: $primary-red;
}

.out_of_stock {
  color: $primary-red;
}

.selectable_attribute_values {
  margin: 0.5rem 0;
}

.product_quantity {
  margin: 0.5rem 0;

  p {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: $primary-dark-text-color;
  }
}

.quantity__controls {
  @include flexbox;
  gap: 0.5rem;

  button {
    width: 28px;
    height: 28px;
    @include flexbox(center, center);
    background-color: #f0f2f2;
    border: 1px solid #d5d9d9;
    border-radius: $border-radius-1;
    cursor: pointer;

    &:hover:not(:disabled) {
      background-color: $sky-lighter-blue;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.5;
    }

    i {
      font-size: $font-size-1;
    }
  }

  input {
    width: 40px;
    height: 28px;
    text-align: center;
    border: 1px solid #d5d9d9;
    border-radius: $border-radius-1;
  }
}

.add_to_cart_btn:disabled {
  background-color: $primary-blue;
  color: #fff;
  opacity: 0.6;
  cursor: not-allowed;
  border: none;
}

.buy_now_btn {
  @include btn(#fff, $primary-blue);
  text-transform: uppercase;
  width: 100%;
  padding: .6rem 0rem;
  transition: all .2s ease-in-out;

  &:hover {
    background-color: darken($primary-blue, 5%);
  }
}

.next_to_select {
  width: fit-content;
  border-radius: 6px;
  padding: .1rem .5rem;
  background: $sky-lighter-blue;
  transition: background 0.3s, box-shadow 0.3s;
}

.checkout {
  margin: 1.5rem 0;
  display: grid;
  gap: 1rem;
  width: 100%;

  // Mobile: stack everything vertically
  grid-template-columns: 1fr;
  grid-template-areas:
    "add-to-cart"
    "buy-now"
    "wishlist";

  button {
    @include btn(#fff, $primary-blue);
    text-transform: uppercase;
    width: 100%;
    padding: .6rem 0rem;
    transition: all .2s ease-in-out;

    &:hover {
      background-color: darken($primary-blue, 5%);
    }
  }

  // Target the Add to Cart button specifically
  .add_to_cart_btn {
    grid-area: add-to-cart;
  }

  // Target the Buy Now button specifically
  .buy_now_btn {
    grid-area: buy-now;
  }

  // Target the WishlistButton wrapper
  .wishlist_button {
    grid-area: wishlist;
    @include flexbox(center, center);
  }

  // Tablet and up: show buttons side by side with wishlist on the right
  @media (width > 425px) {
    grid-template-columns: 1fr 1fr auto;
    grid-template-areas: "add-to-cart buy-now wishlist";
    align-items: center;
  }

  // Alternative layout: wishlist button takes full width on mobile
  // @media (width > 425px) {
  //   grid-template-columns: 1fr 1fr;
  //   grid-template-areas: 
  //     "add-to-cart buy-now"
  //     "wishlist wishlist";
  // }
}