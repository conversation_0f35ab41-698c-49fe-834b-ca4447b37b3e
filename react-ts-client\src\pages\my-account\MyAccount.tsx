import { Link, Outlet, useNavigate } from 'react-router-dom'
import { IoMd<PERSON>erson } from "react-icons/io"
import { FaBoxArchive } from "react-icons/fa6"
import { PiListHeartFill } from "react-icons/pi"
import styles from './MyAccount.module.scss'
import authStore from '../../zustand/authStore'
import { useEffect } from 'react'

const MyAccount = () => {

  const { isLoggedIn } = authStore()
  const navigate = useNavigate()

  useEffect(() => {
    // replace: true prevents back navigation clearing the history stack.
    if (!isLoggedIn) {
      navigate('user/login', { replace: true })
    }
  }, [isLoggedIn, navigate])

  return (
    <div className={`${styles.my_account} container`}>
      <section className={styles.navbar}>
        <h3>Hello, User</h3>
        <div className={styles.navbar__links}>
          <div className={styles.navbar__links__links}>
            <Link to='/customer'>
              <i><IoMdPerson /></i>
              <span>My Profile</span>
            </Link>
            <Link to="my-orders">
              <i><FaBoxArchive /></i>
              <span>My Orders</span>
            </Link>
            <Link to="wishlist">
              <i><PiListHeartFill /></i>
              <span>My WishList</span>
            </Link>
          </div>
        </div>
      </section>
      <section className={styles.my_account__content}>
        <Outlet />
      </section>
    </div>
  )
}

export default MyAccount
