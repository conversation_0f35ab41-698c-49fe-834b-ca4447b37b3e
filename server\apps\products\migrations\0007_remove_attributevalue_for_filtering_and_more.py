# Generated by Django 5.1.6 on 2025-05-13 06:56

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('products', '0006_productattributevalue_product_attribute_value'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='attributevalue',
            name='for_filtering',
        ),
        migrations.RemoveField(
            model_name='attributevalue',
            name='selectable',
        ),
        migrations.AddField(
            model_name='producttypeattribute',
            name='is_filterable',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='producttypeattribute',
            name='is_option_selector',
            field=models.BooleanField(default=False),
        ),
    ]
