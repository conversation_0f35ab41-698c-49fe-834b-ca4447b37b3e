import { useMutation, useQueryClient } from '@tanstack/react-query'
import APIClient from "../../services/api-client"
import { UpdateCustomerShape } from '../../../pages/auth/register/4-customer/UpdateCustomer'
import { CUSTOMER_DETAILS } from '../constants'


const useUpdateCustomer = () => {
  const queryClient = useQueryClient()

  const apiClient = new APIClient(`/customers/me/`)

  const mutation = useMutation({
    mutationFn: (customerData: Partial<UpdateCustomerShape>) => apiClient.patch(customerData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [CUSTOMER_DETAILS]
      })
    }
  })

  return { mutation }
}

export default useUpdateCustomer