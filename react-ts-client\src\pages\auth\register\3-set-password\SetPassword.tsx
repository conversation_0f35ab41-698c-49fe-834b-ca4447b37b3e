import { use<PERSON><PERSON>, SubmitHandler } from 'react-hook-form'
import { useNavigate } from 'react-router-dom'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { setPasswordSchema } from '../../../../types/schemas'
import Underlay from '../../../../components/utils/underlay/Underlay'
import Logo from '../../../../components/utils/logo/Logo'
import loading_blue from '../../../../assets/loading_svg_blue.svg'
import Alert from '../../../../components/utils/alert/Alert'
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import useSetPassword from '../../../../react-query/hooks/auth-hooks/registration/useSetPassword'
import { FaEye, FaEyeSlash } from 'react-icons/fa'
import useTogglePasswordVisibility from '../../../../hooks/useTogglePasswordVisibility'
import styles from './SetPassword.module.scss'
import authStore from '../../../../zustand/authStore'
import { useEffect } from 'react'

export type passwordSchemaFormInputs = z.infer<typeof setPasswordSchema>

const SetPassword = () => {
  const navigate = useNavigate()
  const { mutation } = useSetPassword()
  const { username, regInitiated, passwordSubmitted, setIsLoggedIn, setPasswordSubmitted } = authStore()

  const { isVisible: isNewPasswordVisible, toggleVisibility: toggleNewPasswordVisibility } = useTogglePasswordVisibility()
  const { isVisible: isReNewPasswordVisible, toggleVisibility: toggleReNewPasswordVisibility } = useTogglePasswordVisibility()

  const { register, handleSubmit, formState: { errors } } = useForm<passwordSchemaFormInputs>({
    resolver: zodResolver(setPasswordSchema)
  })

  console.log('PasswordSubmitted in SetPassword', passwordSubmitted)
  console.log('Username in SetPassword', username)

  const onSubmit: SubmitHandler<passwordSchemaFormInputs> = async (data) => {
    const { password, confirm_password } = data
    mutation.mutate({
      password,
      confirm_password,
    }, {
      onSuccess: () => {
        // setUsername(null)
        setPasswordSubmitted(true)
        setIsLoggedIn(true)
        navigate('/user/update-customer/')
      },
    })
  }

  useEffect(() => {
    if (passwordSubmitted) {
      navigate('/user/update-customer/')
    }
  })

  useEffect(() => {
    if (!regInitiated) {
      navigate('/user/register') // or whichever page you'd like to send them to
    }
  }, [regInitiated, navigate])

  return (
    <Underlay isOpen>
      {!passwordSubmitted && regInitiated && <div className={styles.new_password}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
        <form onSubmit={handleSubmit(onSubmit)} className='form'>
          <Alert variant="success" message="Account created. Let's set a password for your account." highlightWords={["Account created."]} />
          <div className='form_group'>
            <label className='form_label' htmlFor="password">Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                type={isNewPasswordVisible ? "text" : "password"}
                id="password"
                {...register('password')}
              />
              <span onClick={toggleNewPasswordVisibility}>
                <i>{isNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.password && <p className='form_error'>{errors.password.message} &#128543;</p>}
          </div>
          <div className='form_group'>
            <label className='form_label' htmlFor="confirm_password">Confirm Password:</label>
            <section className='password__container'>
              <input
                className='form_input'
                type={isReNewPasswordVisible ? "text" : "password"}
                id="confirm_password"
                {...register('confirm_password')}
              />
              <span onClick={toggleReNewPasswordVisibility}>
                <i>{isReNewPasswordVisible ? <FaEyeSlash /> : <FaEye />}</i>
              </span>
            </section>
            {errors.confirm_password && <p className='form_error'>{errors.confirm_password.message} &#128543;</p>}
          </div>
          <section className='btn_container'>
            {/* <button
                type="button"
                className='empty_btn'
                disabled={mutation.isPending}
                onClick={() => navigate('/customer')}>Cancel
              </button> */}
            <button type="submit" className='empty_btn' disabled={mutation.isPending}>
              {mutation.isPending ? (
                <img src={loading_blue} alt="Loading..." className='loading_svg' />
              ) : (
                'Set Password'
              )}
            </button>
          </section>
        </form>
      </div>}
    </Underlay>
  )
}

export default SetPassword
