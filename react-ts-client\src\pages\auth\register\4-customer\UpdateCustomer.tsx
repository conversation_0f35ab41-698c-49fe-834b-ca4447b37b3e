import { useF<PERSON>, SubmitHandler } from 'react-hook-form'
import { useEffect, useState } from 'react'
import { z } from 'zod'
import Logo from '../../../../components/utils/logo/Logo'
import { zodResolver } from '@hookform/resolvers/zod'
import Alert from '../../../../components/utils/alert/Alert'
import Modal from '../../../../components/utils/modal/Modal' // Importing the Modal component
import { getErrorMessage } from '../../../../components/utils/getErrorMessage'
import { AxiosError } from 'axios'
import { ErrorResponse } from '../../../../types/types'
import useUpdateCustomer from '../../../../react-query/hooks/customer-related-hooks/useUpdateCustomer'
import { updateCustomerSchema } from '../../../../types/schemas'
import { useNavigate } from 'react-router-dom'
import authStore from '../../../../zustand/authStore'
import loading_svg from '../../../../assets/loading_svg_white.svg'
import styles from './UpdateCustomer.module.scss'

export type UpdateCustomerShape = z.infer<typeof updateCustomerSchema>

const UpdateCustomer = () => {
  const navigate = useNavigate()
  const { customerDetailsSubmitted, setCustomerDetailsSubmitted } = authStore()
  const { mutation } = useUpdateCustomer()
  const [showUnderageModal, setShowUnderageModal] = useState(false) // State to control modal visibility

  const { register, handleSubmit, formState: { errors }, trigger } = useForm<UpdateCustomerShape>({
    resolver: zodResolver(updateCustomerSchema)
  })

  const handleUnderageClick = () => {
    // Show the modal when the button is clicked
    setShowUnderageModal(true)
  }

  const onSubmit: SubmitHandler<UpdateCustomerShape> = async (data) => {
    const isValid = await trigger('birth_date') // Manually trigger validation for birth_date

    if (!isValid) {
      // If birth_date validation fails, show the modal
      setShowUnderageModal(true)
      return
    }

    // If all validations pass, proceed with submitting the form
    mutation.mutate(data, {
      onSuccess: () => {
        // setUsername(null)
        // setPasswordSubmitted(true)
        setCustomerDetailsSubmitted(true)
        navigate('/user/update-auth-info/')
      },
    })
  }

  useEffect(() => {
    if (customerDetailsSubmitted) {
      navigate('/user/update-auth-info/')
    }
  }, [customerDetailsSubmitted, navigate])

  // useEffect(() => {
  //   if (!regInitiated) {
  //     navigate('/user/register') // or whichever page you'd like to send them to
  //   }
  // }, [regInitiated, navigate])

  const handleModalConfirm = () => {
    // Handle the case where the user confirms they are under 18
    console.log('User is under 18 but confirmed being underage.')
    setShowUnderageModal(false) // Close the modal
  }

  const handleModalClose = () => {
    // Handle the case where the user cancels and wants to correct their birth date
    setShowUnderageModal(false)
  }

  return (
    <div className={styles.register_container}>
      <div className={`${styles.form_container} container`}>
        <div className='logo_header'>
          <Logo />
        </div>
        {mutation.isSuccess ? (
          <Alert variant="success" message='Customer updated successfully.' />
        ) : (
          <>
            {mutation.error && <Alert variant="error" message={getErrorMessage(mutation.error as AxiosError<ErrorResponse>)} />}
            {/* {mutation.isPending && <Spinner color='#0091CF' size={20} loading />} */}
            <h2 className='title'>Let's update your profile</h2>
            <form onSubmit={handleSubmit(onSubmit)} className='form'>
              <div className='form_group'>
                <label className='form_label' htmlFor="first_name">First Name:</label>
                <input className='form_input' type="text" id="first_name" {...register("first_name")} />
                {errors.first_name && <p className='form_error'>{errors.first_name.message} &#128543;</p>}
              </div>

              <div className='form_group'>
                <label className='form_label' htmlFor="last_name">Last Name:</label>
                <input className='form_input' type="text" id="last_name" {...register("last_name")} />
                {errors.last_name && <p className='form_error'>{errors.last_name.message} &#128543;</p>}
              </div>

              <div className='form_group'>
                <label className='form_label' htmlFor="birth_date">Date of birth:</label>
                <input className='form_input' type="date" id="birth_date" {...register("birth_date")} />
                {errors.birth_date && <p className='form_error'>{errors.birth_date.message} &#128543;</p>}
              </div>

              {/* Conditionally render the "I am not eighteen" button */}
              {errors.birth_date && (
                <button type="button" onClick={handleUnderageClick} className={styles.underageButton}>
                  I am not eighteen or older
                </button>
              )}

              <button type="submit" disabled={mutation.isPending}>
                {mutation.isPending ? (
                  <img src={loading_svg} alt="Loading..." className='loading_svg' />
                ) : (
                  'Update'
                )}
              </button>
            </form>

            {/* Modal for confirmation */}
            <Modal
              title="Underage Confirmation"
              message="Are you sure you are not 18 years or older?"
              show={showUnderageModal}
              onClose={handleModalClose}
              onConfirm={handleModalConfirm}
              btn1='I am sure'
              btn2="It's mistake"
            />
          </>
        )}
      </div>
    </div>
  )
}

export default UpdateCustomer
