@import '../../../scss/variables';
@import '../../../scss/mixins';

.search {
  position: relative;
  width: 100%;

  form {
    @include flexbox(flex-start, stretch);
    width: 100%;

    input {
      flex-grow: 1;
      padding: 0.2rem .3rem;
      border: 1px solid #ccc;
      border-radius: 4px 0 0 0;
      font-size: 1rem;

      &:focus {
        outline: none;
      }

      &::selection {
        background-color: $sky-lighter-blue;
        color: $primary-dark-blue;
      }
    }

    button {
      @include btn(#fff, $primary-dark-blue);
      border: none;
      border-radius: 0 4px 4px 0;
      padding: .6rem;
      cursor: pointer;
      @include flexbox(center, center);

      i {
        font-size: 1.3rem;
      }
    }
  }

  .search_suggestions {
    position: absolute;
    // top: 100%;
    // left: 0;
    width: 100%;
    z-index: 5;

    .backdrop {
      position: fixed;
      // top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      background-color: rgba(0, 0, 0, 0.4);
      z-index: 4;
    }

    .suggestions {
      position: relative;
      background-color: #fff;
      border: 1px solid #ccc;
      border-top: none;
      border-radius: 0 0 4px 4px;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      z-index: 6;
      max-height: 300px;
      overflow-y: auto;
      // padding: 0.5rem;
    }

    .category_item {
      // padding: 0 0 .5rem 0;
      // background-color: $sky-light-blue;
      // margin: 0.3rem 0;
      border-radius: 4px;
      transition: background-color 0.3s ease;

      // Add hover styles here if needed

      a {
        padding: .3rem;
        margin: 0 0 .3rem 0;
        // background-color: $sky-lighter-blue;
        color: $primary-dark-text-color;
        // text-decoration: none;
        // display: block;
        transition: all .3s ease;

        &:hover {
          text-decoration: underline;
          color: $primary-dark-blue;

        }
      }

      // Category with children styles can be added here when needed

      // Child categories in a horizontal row for nested categories
      .child_categories {
        // background-color: red;
        // width: fit-content;
        // color: $sky-lighter-blue;
        @include flexbox(flex-start, flex-start, row, wrap);
        gap: 5px;
        // margin-top: 0.5rem;
        padding: 0 0 0 5px;
        border-left: 1px solid $sky-light-blue;

        a {
          width: fit-content;
          padding: 0.3rem 0.8rem;
          background-color: $sky-lighter-blue;
          color: $primary-dark-blue;
          border-radius: $border-radius-1;
          transition: all .2s ease;

          &:hover {
            background-color: darken($sky-lighter-blue, 5%);
          }
        }
      }
    }

    // For categories without children, display in a regular list format
    .category_item:not(.hasChildren) {
      display: block;

      a {
        display: block;
      }
    }

    .no_suggestions {
      display: block;
      padding: 0.5rem;
      color: $primary-lighter-text-color;
      font-style: italic;
    }
  }
}