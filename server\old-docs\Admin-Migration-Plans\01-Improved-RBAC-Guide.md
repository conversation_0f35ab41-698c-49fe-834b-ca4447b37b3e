# 01: Comprehensive RBAC Implementation Guide

## **Current System Analysis**

### **Existing Infrastructure:**

- ✅ Custom User model with email/phone authentication (`apps.core.models.User`)
- ✅ JWT authentication with `CustomJWTAuthentication`
- ✅ Basic permission classes (`IsAdminOrReadOnly`, `IsAdminOrGroupMember`)
- ✅ Django groups system partially implemented
- ✅ Redis available for caching and token management
- ✅ Celery for background tasks

### **Integration Points:**

- **User Model**: `apps.core.models.User` (extends AbstractBaseUser, PermissionsMixin)
- **Authentication**: `apps.core.authentication.CustomJWTAuthentication`
- **Permissions**: `utils.permissions.py` (basic permission classes)
- **Settings**: JWT configuration in `pc_hardware.settings.common`

### **Gaps to Address:**

- ❌ No structured role hierarchy
- ❌ No token revocation mechanism (JTI missing)
- ❌ Limited permission granularity
- ❌ No audit trail system
- ❌ No contextual permissions

---

## **Step-by-Step Implementation Guide**

### **Phase 1: Foundation Setup (Week 1)**

#### **Step 1.1: Create Admin API App**

```bash
# Create new Django app for admin API
python manage.py startapp admin_api apps/admin_api
```

#### **Step 1.2: Update Settings Configuration**

```python
# pc_hardware/settings/common.py - Add to INSTALLED_APPS
INSTALLED_APPS = [
    # ... existing apps
    'apps.admin_api',
]

# Add Redis configuration for token revocation
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    },
    'tokens': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        }
    }
}

# Update JWT settings for admin tokens
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),  # Shorter for security
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ADMIN_ACCESS_TOKEN_LIFETIME': timedelta(hours=8),  # Extended for admin
    'BLACKLIST_AFTER_ROTATION': True,
    'ROTATE_REFRESH_TOKENS': True,
})

# Admin-specific settings
ADMIN_IP_WHITELIST = ['127.0.0.1', '***********/24']  # Configure as needed
ADMIN_SESSION_TIMEOUT = 8 * 60 * 60  # 8 hours
ADMIN_MAX_CONCURRENT_SESSIONS = 3
```

#### **Step 1.3: Create RBAC Models**

```python
# apps/admin_api/models.py
from django.db import models
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey
from django.utils import timezone
import uuid

User = get_user_model()

class Permission(models.Model):
    """Enhanced permission model with contextual support"""
    codename = models.CharField(max_length=100, unique=True, db_index=True)
    name = models.CharField(max_length=255)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['content_type__app_label', 'codename']
        indexes = [
            models.Index(fields=['codename', 'is_active']),
        ]

class Role(models.Model):
    """Hierarchical role model"""
    name = models.CharField(max_length=100, unique=True, db_index=True)
    display_name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    permissions = models.ManyToManyField(Permission, through='RolePermission', blank=True)
    parent_role = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['name']

class RolePermission(models.Model):
    """Through model for role-permission relationships"""
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    granted_at = models.DateTimeField(auto_now_add=True)
    granted_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    class Meta:
        unique_together = ('role', 'permission')

class UserRole(models.Model):
    """User role assignments with context and expiration"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='admin_roles')
    role = models.ForeignKey(Role, on_delete=models.CASCADE)
    assigned_at = models.DateTimeField(auto_now_add=True)
    assigned_by = models.ForeignKey(User, related_name='assigned_roles', on_delete=models.SET_NULL, null=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    context_data = models.JSONField(default=dict, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ('user', 'role')
        indexes = [
            models.Index(fields=['user', 'is_active']),
            models.Index(fields=['expires_at']),
        ]

class ContextualPermission(models.Model):
    """Resource-specific permissions"""
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE)
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    granted_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        unique_together = ('user', 'permission', 'content_type', 'object_id')

class TokenRevocation(models.Model):
    """Token revocation tracking"""
    jti = models.UUIDField(unique=True, db_index=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    revoked_at = models.DateTimeField(auto_now_add=True)
    reason = models.CharField(max_length=100, default='manual_logout')
    expires_at = models.DateTimeField()  # Original token expiry
    
    class Meta:
        indexes = [
            models.Index(fields=['jti', 'expires_at']),
        ]

class AuditLog(models.Model):
    """Comprehensive audit trail"""
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    action = models.CharField(max_length=100, db_index=True)
    resource_type = models.CharField(max_length=100)
    resource_id = models.PositiveIntegerField(null=True)
    old_data = models.JSONField(null=True, blank=True)
    new_data = models.JSONField(null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action', 'timestamp']),
            models.Index(fields=['resource_type', 'resource_id']),
        ]

class SecurityAuditLog(models.Model):
    """Security-specific audit events"""
    event_type = models.CharField(max_length=50, db_index=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField(blank=True)
    details = models.JSONField(default=dict)
    timestamp = models.DateTimeField(auto_now_add=True, db_index=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['event_type', 'timestamp']),
            models.Index(fields=['ip_address', 'timestamp']),
        ]
```

#### **Step 1.4: Create Enhanced JWT Authentication**

```python
# apps/admin_api/authentication.py
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.tokens import AccessToken
from rest_framework_simplejwt.exceptions import InvalidToken, AuthenticationFailed
from django.core.cache import caches
from django.contrib.auth import get_user_model
import uuid
import json

User = get_user_model()
token_cache = caches['tokens']

class AdminAccessToken(AccessToken):
    """Enhanced access token with JTI and admin claims"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add JTI (JWT ID) for revocation tracking
        if 'jti' not in self.payload:
            self.payload['jti'] = str(uuid.uuid4())
    
    @classmethod
    def for_user(cls, user):
        token = super().for_user(user)
        
        # Add admin-specific claims
        if user.is_staff:
            from .services import RoleService
            role_service = RoleService()
            
            token['roles'] = role_service.get_user_roles(user)
            token['permissions'] = role_service.get_user_permissions(user)
            token['context'] = role_service.get_user_context(user)
            token['session_type'] = 'admin'
        
        return token

class AdminJWTAuthentication(JWTAuthentication):
    """Enhanced JWT authentication with revocation checking"""
    
    def get_validated_token(self, raw_token):
        token = super().get_validated_token(raw_token)
        
        # Check if token is revoked
        jti = token.get('jti')
        if jti and self.is_token_revoked(jti):
            raise InvalidToken('Token has been revoked')
        
        return token
    
    def is_token_revoked(self, jti):
        """Check if token is in revocation list"""
        return token_cache.get(f'revoked:{jti}') is not None
    
    def authenticate(self, request):
        result = super().authenticate(request)
        if result:
            user, token = result
            # Additional security checks for admin users
            if user.is_staff:
                self._validate_admin_session(request, user, token)
        return result
    
    def _validate_admin_session(self, request, user, token):
        """Additional validation for admin sessions"""
        from .services import SecurityService
        security_service = SecurityService()
        
        # IP whitelist check
        if not security_service.is_ip_whitelisted(request):
            raise AuthenticationFailed('IP address not whitelisted')
        
        # Concurrent session check
        if security_service.exceeds_session_limit(user):
            raise AuthenticationFailed('Too many concurrent sessions')
```

#### **Step 1.5: Create Core Services**

```python
# apps/admin_api/services.py
from django.core.cache import caches
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Role, UserRole, Permission, TokenRevocation, AuditLog
import ipaddress
from django.conf import settings

User = get_user_model()
cache = caches['default']
token_cache = caches['tokens']

class RoleService:
    """Service for role and permission management"""
    
    def get_user_roles(self, user):
        """Get active roles for user"""
        cache_key = f'user_roles:{user.id}'
        roles = cache.get(cache_key)
        
        if roles is None:
            user_roles = UserRole.objects.filter(
                user=user,
                is_active=True,
                role__is_active=True
            ).select_related('role')
            
            # Check expiration
            now = timezone.now()
            roles = []
            for user_role in user_roles:
                if not user_role.expires_at or user_role.expires_at > now:
                    roles.append(user_role.role.name)
            
            cache.set(cache_key, roles, 300)  # 5 minutes
        
        return roles
    
    def get_user_permissions(self, user):
        """Get all permissions for user including inherited"""
        cache_key = f'user_permissions:{user.id}'
        permissions = cache.get(cache_key)
        
        if permissions is None:
            permissions = set()
            roles = self.get_user_roles(user)
            
            for role_name in roles:
                role_perms = self._get_role_permissions(role_name)
                permissions.update(role_perms)
            
            permissions = list(permissions)
            cache.set(cache_key, permissions, 300)
        
        return permissions
    
    def _get_role_permissions(self, role_name):
        """Get permissions for a specific role including inherited"""
        cache_key = f'role_permissions:{role_name}'
        permissions = cache.get(cache_key)
        
        if permissions is None:
            try:
                role = Role.objects.get(name=role_name, is_active=True)
                permissions = set()
                
                # Get direct permissions
                direct_perms = role.permissions.filter(is_active=True).values_list('codename', flat=True)
                permissions.update(direct_perms)
                
                # Get inherited permissions from parent roles
                parent = role.parent_role
                while parent and parent.is_active:
                    parent_perms = parent.permissions.filter(is_active=True).values_list('codename', flat=True)
                    permissions.update(parent_perms)
                    parent = parent.parent_role
                
                permissions = list(permissions)
                cache.set(cache_key, permissions, 600)  # 10 minutes
            except Role.DoesNotExist:
                permissions = []
        
        return permissions
    
    def get_user_context(self, user):
        """Get contextual data for user"""
        user_roles = UserRole.objects.filter(
            user=user,
            is_active=True
        ).values_list('context_data', flat=True)
        
        context = {}
        for role_context in user_roles:
            if role_context:
                context.update(role_context)
        
        return context
    
    def invalidate_user_cache(self, user):
        """Clear user-related caches"""
        cache.delete(f'user_roles:{user.id}')
        cache.delete(f'user_permissions:{user.id}')

class SecurityService:
    """Service for security-related operations"""
    
    def is_ip_whitelisted(self, request):
        """Check if request IP is whitelisted"""
        client_ip = self.get_client_ip(request)
        whitelist = getattr(settings, 'ADMIN_IP_WHITELIST', [])
        
        if not whitelist:
            return True  # No whitelist configured
        
        try:
            client_addr = ipaddress.ip_address(client_ip)
            for allowed in whitelist:
                if '/' in allowed:
                    if client_addr in ipaddress.ip_network(allowed):
                        return True
                else:
                    if client_addr == ipaddress.ip_address(allowed):
                        return True
        except ValueError:
            return False
        
        return False
    
    def get_client_ip(self, request):
        """Get client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def exceeds_session_limit(self, user):
        """Check if user exceeds concurrent session limit"""
        max_sessions = getattr(settings, 'ADMIN_MAX_CONCURRENT_SESSIONS', 3)
        active_sessions = token_cache.get(f'active_sessions:{user.id}', 0)
        return active_sessions >= max_sessions
    
    def track_session(self, user, jti):
        """Track active session"""
        session_key = f'active_sessions:{user.id}'
        sessions = token_cache.get(f'user_sessions:{user.id}', set())
        sessions.add(jti)
        
        token_cache.set(f'user_sessions:{user.id}', sessions, 86400)  # 24 hours
        token_cache.set(session_key, len(sessions), 86400)

class TokenService:
    """Service for token management"""
    
    def revoke_token(self, jti, user, reason='manual_logout'):
        """Revoke a specific token"""
        # Add to revocation cache
        token_cache.set(f'revoked:{jti}', True, 86400)  # 24 hours
        
        # Store in database for audit
        TokenRevocation.objects.create(
            jti=jti,
            user=user,
            reason=reason,
            expires_at=timezone.now() + timezone.timedelta(days=1)
        )
    
    def revoke_all_user_tokens(self, user, reason='security_breach'):
        """Revoke all tokens for a user"""
        sessions = token_cache.get(f'user_sessions:{user.id}', set())
        for jti in sessions:
            self.revoke_token(jti, user, reason)
        
        # Clear session tracking
        token_cache.delete(f'user_sessions:{user.id}')
        token_cache.delete(f'active_sessions:{user.id}')

class AuditService:
    """Service for audit logging"""
    
    def log_admin_action(self, user, action, resource, old_data=None, new_data=None, request=None):
        """Log admin action"""
        AuditLog.objects.create(
            user=user,
            action=action,
            resource_type=resource.__class__.__name__,
            resource_id=getattr(resource, 'id', None),
            old_data=old_data,
            new_data=new_data,
            ip_address=self._get_ip(request) if request else '127.0.0.1',
            user_agent=request.META.get('HTTP_USER_AGENT', '') if request else ''
        )
    
    def _get_ip(self, request):
        """Extract IP from request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR', '127.0.0.1')
```

#### **Step 1.6: Create Database Migration**

```bash
# Generate and run migrations
python manage.py makemigrations admin_api
python manage.py migrate
```

---

### **Phase 2: Permission System Implementation (Week 2)**

#### **Step 2.1: Create Enhanced Permission Classes**

```python
# apps/admin_api/permissions.py
from rest_framework.permissions import BasePermission
from rest_framework.exceptions import PermissionDenied
from django.core.cache import caches
from .services import RoleService, SecurityService, AuditService
import logging

logger = logging.getLogger(__name__)
cache = caches['default']

class AdminRolePermission(BasePermission):
    """
    Role-based permission checking for admin APIs
    Usage: permission_classes = [AdminRolePermission]
    Required attributes on view: required_roles, required_permissions
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        if not request.user.is_staff:
            return False

        # Get required roles and permissions from view
        required_roles = getattr(view, 'required_roles', [])
        required_permissions = getattr(view, 'required_permissions', [])

        if not required_roles and not required_permissions:
            # If no specific requirements, allow staff users
            return True

        role_service = RoleService()
        user_roles = role_service.get_user_roles(request.user)
        user_permissions = role_service.get_user_permissions(request.user)

        # Check roles
        if required_roles:
            if not any(role in user_roles for role in required_roles):
                self._log_permission_denied(request, 'insufficient_roles', {
                    'required_roles': required_roles,
                    'user_roles': user_roles
                })
                return False

        # Check permissions
        if required_permissions:
            if not any(perm in user_permissions for perm in required_permissions):
                self._log_permission_denied(request, 'insufficient_permissions', {
                    'required_permissions': required_permissions,
                    'user_permissions': user_permissions
                })
                return False

        return True

    def has_object_permission(self, request, view, obj):
        """Object-level permission checking"""
        if not self.has_permission(request, view):
            return False

        # Check contextual permissions
        contextual_perms = getattr(view, 'contextual_permissions', {})
        if contextual_perms:
            return self._check_contextual_permissions(request, obj, contextual_perms)

        return True

    def _check_contextual_permissions(self, request, obj, contextual_perms):
        """Check resource-specific permissions"""
        role_service = RoleService()
        user_context = role_service.get_user_context(request.user)

        for context_key, allowed_values in contextual_perms.items():
            obj_value = getattr(obj, context_key, None)
            user_allowed = user_context.get(f'{context_key}_access', [])

            if obj_value and obj_value not in user_allowed:
                return False

        return True

    def _log_permission_denied(self, request, reason, details):
        """Log permission denial for audit"""
        audit_service = AuditService()
        audit_service.log_security_event(
            event_type='permission_denied',
            user=request.user,
            ip_address=SecurityService().get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={'reason': reason, **details}
        )

class BulkOperationPermission(AdminRolePermission):
    """Special permission for bulk operations"""

    def has_permission(self, request, view):
        if not super().has_permission(request, view):
            return False

        # Additional checks for bulk operations
        if hasattr(view, 'action') and 'bulk' in view.action:
            bulk_permissions = getattr(view, 'bulk_permissions', [])
            if bulk_permissions:
                role_service = RoleService()
                user_permissions = role_service.get_user_permissions(request.user)

                if not any(perm in user_permissions for perm in bulk_permissions):
                    return False

        return True

class SuperAdminRequired(BasePermission):
    """Require SuperAdmin role"""

    def has_permission(self, request, view):
        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        role_service = RoleService()
        user_roles = role_service.get_user_roles(request.user)
        return 'SuperAdmin' in user_roles

class ResourceOwnerOrAdmin(AdminRolePermission):
    """Allow resource owners or admins with specific roles"""

    def has_object_permission(self, request, view, obj):
        # Check if user owns the resource
        if hasattr(obj, 'user') and obj.user == request.user:
            return True

        if hasattr(obj, 'customer') and hasattr(obj.customer, 'user') and obj.customer.user == request.user:
            return True

        # Otherwise check admin permissions
        return super().has_object_permission(request, view, obj)
```

#### **Step 2.2: Create Permission Checker Service**

```python
# apps/admin_api/services.py (add to existing file)

class PermissionChecker:
    """Centralized permission checking service"""

    def __init__(self):
        self.cache = caches['default']
        self.role_service = RoleService()

    def has_permission(self, user, permission, resource=None, context=None):
        """Check if user has specific permission"""
        # Build cache key
        context_hash = hash(str(context)) if context else 0
        resource_key = f"{resource.__class__.__name__}:{resource.id}" if resource else "global"
        cache_key = f"perm:{user.id}:{permission}:{resource_key}:{context_hash}"

        # Check cache first
        result = self.cache.get(cache_key)
        if result is not None:
            return result

        # Check permissions
        result = self._check_permission_logic(user, permission, resource, context)

        # Cache result for 5 minutes
        self.cache.set(cache_key, result, 300)
        return result

    def _check_permission_logic(self, user, permission, resource, context):
        """Core permission checking logic"""
        # 1. Check if user is active and staff
        if not user.is_active or not user.is_staff:
            return False

        # 2. SuperAdmin has all permissions
        user_roles = self.role_service.get_user_roles(user)
        if 'SuperAdmin' in user_roles:
            return True

        # 3. Check direct permissions
        user_permissions = self.role_service.get_user_permissions(user)
        if permission in user_permissions:
            # 4. Check contextual restrictions if resource provided
            if resource and context:
                return self._check_contextual_access(user, resource, context)
            return True

        return False

    def _check_contextual_access(self, user, resource, context):
        """Check contextual access restrictions"""
        user_context = self.role_service.get_user_context(user)

        # Check brand access
        if hasattr(resource, 'brand_id'):
            allowed_brands = user_context.get('brand_access', [])
            if allowed_brands and resource.brand_id not in allowed_brands:
                return False

        # Check category access
        if hasattr(resource, 'category_id'):
            allowed_categories = user_context.get('category_access', [])
            if allowed_categories and resource.category_id not in allowed_categories:
                return False

        # Check regional access
        if hasattr(resource, 'region'):
            allowed_regions = user_context.get('region_access', [])
            if allowed_regions and resource.region not in allowed_regions:
                return False

        return True

    def bulk_check_permissions(self, user, permission_requests):
        """Efficient bulk permission checking"""
        results = {}

        for request_id, (permission, resource, context) in permission_requests.items():
            results[request_id] = self.has_permission(user, permission, resource, context)

        return results

    def invalidate_cache(self, user=None, permission=None):
        """Invalidate permission cache"""
        if user:
            # Clear all permissions for specific user
            cache_pattern = f"perm:{user.id}:*"
            # Note: Redis pattern deletion would be implemented here
            # For now, we'll clear the role service cache
            self.role_service.invalidate_user_cache(user)
        else:
            # Clear all permission cache (use with caution)
            self.cache.clear()
```

#### **Step 2.3: Create Middleware for Security**

```python
# apps/admin_api/middleware.py
from django.http import JsonResponse
from django.core.cache import caches
from django.conf import settings
from .services import SecurityService, AuditService
import time
import json

class AdminSecurityMiddleware:
    """Enhanced security middleware for admin APIs"""

    def __init__(self, get_response):
        self.get_response = get_response
        self.security_service = SecurityService()
        self.audit_service = AuditService()
        self.cache = caches['default']

    def __call__(self, request):
        # Only apply to admin API paths
        if not request.path.startswith('/api/admin/'):
            return self.get_response(request)

        # Skip for non-authenticated requests (will be handled by auth)
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return self.get_response(request)

        # Apply security checks
        security_check = self._perform_security_checks(request)
        if security_check:
            return security_check

        # Process request
        start_time = time.time()
        response = self.get_response(request)
        end_time = time.time()

        # Log admin action
        self._log_admin_action(request, response, end_time - start_time)

        return response

    def _perform_security_checks(self, request):
        """Perform various security checks"""
        user = request.user

        # 1. IP Whitelist check
        if not self.security_service.is_ip_whitelisted(request):
            self.audit_service.log_security_event(
                event_type='ip_blocked',
                user=user,
                ip_address=self.security_service.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'path': request.path}
            )
            return JsonResponse({
                'success': False,
                'error': 'Access denied: IP not whitelisted'
            }, status=403)

        # 2. Rate limiting
        if self._is_rate_limited(request):
            self.audit_service.log_security_event(
                event_type='rate_limit_exceeded',
                user=user,
                ip_address=self.security_service.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'path': request.path}
            )
            return JsonResponse({
                'success': False,
                'error': 'Rate limit exceeded'
            }, status=429)

        # 3. Session validation
        if user.is_staff and self.security_service.exceeds_session_limit(user):
            return JsonResponse({
                'success': False,
                'error': 'Too many concurrent sessions'
            }, status=403)

        return None

    def _is_rate_limited(self, request):
        """Check rate limiting"""
        user = request.user
        ip = self.security_service.get_client_ip(request)

        # Different limits for different user types
        if 'SuperAdmin' in getattr(user, 'admin_roles', []):
            limit = 1000  # requests per hour
        elif user.is_staff:
            limit = 500   # requests per hour
        else:
            limit = 100   # requests per hour

        # Check rate limit
        cache_key = f'rate_limit:{user.id}:{ip}'
        current_count = self.cache.get(cache_key, 0)

        if current_count >= limit:
            return True

        # Increment counter
        self.cache.set(cache_key, current_count + 1, 3600)  # 1 hour
        return False

    def _log_admin_action(self, request, response, duration):
        """Log admin API actions"""
        if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
            self.audit_service.log_admin_action(
                user=request.user,
                action=f"{request.method}:{request.path}",
                resource=None,  # Will be set by view if needed
                old_data=None,
                new_data=getattr(request, 'data', None),
                request=request
            )
```

#### **Step 2.4: Update Settings for Middleware**

```python
# pc_hardware/settings/common.py - Add to MIDDLEWARE
MIDDLEWARE = [
    # ... existing middleware
    'apps.admin_api.middleware.AdminSecurityMiddleware',
    # ... rest of middleware
]

# Add logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'admin_audit.log',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.FileHandler',
            'filename': 'security_audit.log',
        },
    },
    'loggers': {
        'apps.admin_api': {
            'handlers': ['file'],
            'level': 'INFO',
            'propagate': True,
        },
        'security': {
            'handlers': ['security_file'],
            'level': 'WARNING',
            'propagate': True,
        },
    },
}
```

---

### **Phase 3: Role Hierarchy & Data Setup (Week 3)**

#### **Step 3.1: Create Management Commands for Role Setup**

```python
# apps/admin_api/management/__init__.py
# (empty file)

# apps/admin_api/management/commands/__init__.py
# (empty file)

# apps/admin_api/management/commands/setup_rbac.py
from django.core.management.base import BaseCommand
from django.contrib.contenttypes.models import ContentType
from apps.admin_api.models import Permission, Role, RolePermission
from apps.products.models import Product, Category, Brand, ProductType
from apps.order.models import Order
from apps.customers.models import Customer
from apps.core.models import User

class Command(BaseCommand):
    help = 'Setup RBAC roles and permissions'

    def handle(self, *args, **options):
        self.stdout.write('Setting up RBAC system...')

        # Create permissions
        self.create_permissions()

        # Create roles
        self.create_roles()

        # Assign permissions to roles
        self.assign_permissions()

        self.stdout.write(self.style.SUCCESS('RBAC setup completed successfully!'))

    def create_permissions(self):
        """Create all required permissions"""
        self.stdout.write('Creating permissions...')

        # Define permission structure
        permission_structure = {
            'products': ['view', 'add', 'change', 'delete', 'bulk_update', 'export'],
            'categories': ['view', 'add', 'change', 'delete', 'reorder'],
            'brands': ['view', 'add', 'change', 'delete'],
            'product_types': ['view', 'add', 'change', 'delete'],
            'orders': ['view', 'change', 'delete', 'cancel', 'refund', 'export'],
            'customers': ['view', 'add', 'change', 'delete', 'export', 'merge'],
            'users': ['view', 'add', 'change', 'delete', 'impersonate'],
            'system': ['view_logs', 'clear_cache', 'backup', 'restore'],
            'analytics': ['view_sales', 'view_products', 'view_customers', 'export_reports'],
        }

        # Get content types
        content_types = {
            'products': ContentType.objects.get_for_model(Product),
            'categories': ContentType.objects.get_for_model(Category),
            'brands': ContentType.objects.get_for_model(Brand),
            'product_types': ContentType.objects.get_for_model(ProductType),
            'orders': ContentType.objects.get_for_model(Order),
            'customers': ContentType.objects.get_for_model(Customer),
            'users': ContentType.objects.get_for_model(User),
            'system': ContentType.objects.get_for_model(User),  # Using User as placeholder
            'analytics': ContentType.objects.get_for_model(User),  # Using User as placeholder
        }

        # Create permissions
        for resource, actions in permission_structure.items():
            content_type = content_types[resource]
            for action in actions:
                codename = f'{resource}.{action}'
                name = f'Can {action} {resource}'

                permission, created = Permission.objects.get_or_create(
                    codename=codename,
                    defaults={
                        'name': name,
                        'content_type': content_type,
                        'description': f'Permission to {action} {resource}'
                    }
                )

                if created:
                    self.stdout.write(f'  Created permission: {codename}')

    def create_roles(self):
        """Create role hierarchy"""
        self.stdout.write('Creating roles...')

        # Define role hierarchy
        roles_data = [
            # Top level
            {'name': 'SuperAdmin', 'display_name': 'Super Administrator', 'parent': None},

            # Second level
            {'name': 'SystemAdmin', 'display_name': 'System Administrator', 'parent': 'SuperAdmin'},
            {'name': 'BusinessAdmin', 'display_name': 'Business Administrator', 'parent': 'SuperAdmin'},
            {'name': 'FinanceManager', 'display_name': 'Finance Manager', 'parent': 'SuperAdmin'},
            {'name': 'AuditManager', 'display_name': 'Audit Manager', 'parent': 'SuperAdmin'},

            # Third level - Business roles
            {'name': 'ProductManager', 'display_name': 'Product Manager', 'parent': 'BusinessAdmin'},
            {'name': 'OrderManager', 'display_name': 'Order Manager', 'parent': 'BusinessAdmin'},
            {'name': 'CustomerManager', 'display_name': 'Customer Manager', 'parent': 'BusinessAdmin'},
            {'name': 'ContentManager', 'display_name': 'Content Manager', 'parent': 'BusinessAdmin'},

            # Fourth level - Specialized roles
            {'name': 'ProductEditor', 'display_name': 'Product Editor', 'parent': 'ProductManager'},
            {'name': 'CategoryManager', 'display_name': 'Category Manager', 'parent': 'ProductManager'},
            {'name': 'InventoryManager', 'display_name': 'Inventory Manager', 'parent': 'ProductManager'},

            {'name': 'OrderProcessor', 'display_name': 'Order Processor', 'parent': 'OrderManager'},
            {'name': 'OrderAnalyst', 'display_name': 'Order Analyst', 'parent': 'OrderManager'},
            {'name': 'ShippingCoordinator', 'display_name': 'Shipping Coordinator', 'parent': 'OrderManager'},

            {'name': 'CustomerSupport', 'display_name': 'Customer Support', 'parent': 'CustomerManager'},
            {'name': 'CustomerAnalyst', 'display_name': 'Customer Analyst', 'parent': 'CustomerManager'},
            {'name': 'AccountManager', 'display_name': 'Account Manager', 'parent': 'CustomerManager'},

            {'name': 'ReviewModerator', 'display_name': 'Review Moderator', 'parent': 'ContentManager'},
            {'name': 'ContentEditor', 'display_name': 'Content Editor', 'parent': 'ContentManager'},
            {'name': 'SEOManager', 'display_name': 'SEO Manager', 'parent': 'ContentManager'},
        ]

        # Create roles in order (parents first)
        created_roles = {}

        for role_data in roles_data:
            parent_role = None
            if role_data['parent']:
                parent_role = created_roles.get(role_data['parent'])

            role, created = Role.objects.get_or_create(
                name=role_data['name'],
                defaults={
                    'display_name': role_data['display_name'],
                    'parent_role': parent_role,
                    'description': f"Role for {role_data['display_name']}"
                }
            )

            created_roles[role_data['name']] = role

            if created:
                self.stdout.write(f'  Created role: {role_data["name"]}')

    def assign_permissions(self):
        """Assign permissions to roles"""
        self.stdout.write('Assigning permissions to roles...')

        # Define role permissions
        role_permissions = {
            'SuperAdmin': ['*'],  # All permissions

            'SystemAdmin': [
                'system.view_logs', 'system.clear_cache', 'system.backup', 'system.restore',
                'users.view', 'users.add', 'users.change', 'users.delete'
            ],

            'ProductManager': [
                'products.view', 'products.add', 'products.change', 'products.delete', 'products.bulk_update', 'products.export',
                'categories.view', 'categories.add', 'categories.change', 'categories.delete', 'categories.reorder',
                'brands.view', 'brands.add', 'brands.change', 'brands.delete',
                'product_types.view', 'product_types.add', 'product_types.change', 'product_types.delete'
            ],

            'ProductEditor': [
                'products.view', 'products.add', 'products.change',
                'categories.view', 'brands.view', 'product_types.view'
            ],

            'CategoryManager': [
                'categories.view', 'categories.add', 'categories.change', 'categories.delete', 'categories.reorder',
                'products.view'
            ],

            'OrderManager': [
                'orders.view', 'orders.change', 'orders.delete', 'orders.cancel', 'orders.export',
                'customers.view', 'analytics.view_sales'
            ],

            'OrderProcessor': [
                'orders.view', 'orders.change'
            ],

            'CustomerManager': [
                'customers.view', 'customers.add', 'customers.change', 'customers.delete', 'customers.export',
                'analytics.view_customers'
            ],

            'CustomerSupport': [
                'customers.view', 'customers.change',
                'orders.view'
            ],
        }

        # Assign permissions
        for role_name, permissions in role_permissions.items():
            try:
                role = Role.objects.get(name=role_name)

                if permissions == ['*']:
                    # Assign all permissions to SuperAdmin
                    all_permissions = Permission.objects.filter(is_active=True)
                    for permission in all_permissions:
                        RolePermission.objects.get_or_create(
                            role=role,
                            permission=permission
                        )
                else:
                    # Assign specific permissions
                    for perm_codename in permissions:
                        try:
                            permission = Permission.objects.get(codename=perm_codename)
                            RolePermission.objects.get_or_create(
                                role=role,
                                permission=permission
                            )
                        except Permission.DoesNotExist:
                            self.stdout.write(
                                self.style.WARNING(f'Permission not found: {perm_codename}')
                            )

                self.stdout.write(f'  Assigned permissions to role: {role_name}')

            except Role.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(f'Role not found: {role_name}')
                )
```

#### **Step 3.2: Create User Role Assignment Command**

```python
# apps/admin_api/management/commands/assign_role.py
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from apps.admin_api.models import Role, UserRole
from apps.admin_api.services import RoleService

User = get_user_model()

class Command(BaseCommand):
    help = 'Assign role to user'

    def add_arguments(self, parser):
        parser.add_argument('email', type=str, help='User email')
        parser.add_argument('role', type=str, help='Role name')
        parser.add_argument('--context', type=str, help='JSON context data', default='{}')
        parser.add_argument('--expires', type=str, help='Expiration date (YYYY-MM-DD)', default=None)

    def handle(self, *args, **options):
        try:
            user = User.objects.get(email=options['email'])
            role = Role.objects.get(name=options['role'])

            # Parse context data
            import json
            context_data = json.loads(options['context'])

            # Parse expiration date
            expires_at = None
            if options['expires']:
                from datetime import datetime
                expires_at = datetime.strptime(options['expires'], '%Y-%m-%d').date()

            # Create user role assignment
            user_role, created = UserRole.objects.get_or_create(
                user=user,
                role=role,
                defaults={
                    'context_data': context_data,
                    'expires_at': expires_at
                }
            )

            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'Successfully assigned role {role.name} to {user.email}')
                )

                # Clear user cache
                role_service = RoleService()
                role_service.invalidate_user_cache(user)
            else:
                self.stdout.write(
                    self.style.WARNING(f'User {user.email} already has role {role.name}')
                )

        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with email {options["email"]} not found')
            )
        except Role.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Role {options["role"]} not found')
            )
        except json.JSONDecodeError:
            self.stdout.write(
                self.style.ERROR('Invalid JSON in context data')
            )
```

#### **Step 3.3: Run Setup Commands**

```bash
# Setup RBAC system
python manage.py setup_rbac

# Assign SuperAdmin role to your admin user
python manage.py assign_role <EMAIL> SuperAdmin

# Assign other roles with context (example)
python manage.py assign_role <EMAIL> ProductManager \
  --context '{"brand_access": [1, 2, 3], "region_access": ["US-WEST"]}'
```

---

### **Phase 4: Admin API Implementation (Week 4-5)**

#### **Step 4.1: Create Base Admin ViewSets**

```python
# apps/admin_api/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.core.cache import caches
from .permissions import AdminRolePermission, BulkOperationPermission
from .services import AuditService, PermissionChecker
import json

class BaseAdminViewSet(viewsets.ModelViewSet):
    """Base viewset for admin APIs with common functionality"""

    permission_classes = [AdminRolePermission]
    required_roles = []
    required_permissions = []
    bulk_permissions = []
    contextual_permissions = {}

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.audit_service = AuditService()
        self.permission_checker = PermissionChecker()
        self.cache = caches['default']

    def get_queryset(self):
        """Override to add contextual filtering"""
        queryset = super().get_queryset()

        # Apply contextual filtering based on user permissions
        if hasattr(self.request, 'user') and self.request.user.is_authenticated:
            queryset = self.apply_contextual_filtering(queryset)

        return queryset

    def apply_contextual_filtering(self, queryset):
        """Apply user context-based filtering"""
        from .services import RoleService
        role_service = RoleService()
        user_context = role_service.get_user_context(self.request.user)

        # Filter by brand access
        if 'brand_access' in user_context and hasattr(queryset.model, 'brand'):
            queryset = queryset.filter(brand_id__in=user_context['brand_access'])

        # Filter by category access
        if 'category_access' in user_context and hasattr(queryset.model, 'category'):
            queryset = queryset.filter(category_id__in=user_context['category_access'])

        # Filter by region access
        if 'region_access' in user_context and hasattr(queryset.model, 'region'):
            queryset = queryset.filter(region__in=user_context['region_access'])

        return queryset

    def perform_create(self, serializer):
        """Override to add audit logging"""
        instance = serializer.save()

        # Log creation
        self.audit_service.log_admin_action(
            user=self.request.user,
            action='create',
            resource=instance,
            new_data=serializer.validated_data,
            request=self.request
        )

        return instance

    def perform_update(self, serializer):
        """Override to add audit logging"""
        old_data = {}
        if serializer.instance:
            # Capture old data
            old_data = {
                field.name: getattr(serializer.instance, field.name)
                for field in serializer.instance._meta.fields
            }

        instance = serializer.save()

        # Log update
        self.audit_service.log_admin_action(
            user=self.request.user,
            action='update',
            resource=instance,
            old_data=old_data,
            new_data=serializer.validated_data,
            request=self.request
        )

        return instance

    def perform_destroy(self, instance):
        """Override to add audit logging"""
        # Capture data before deletion
        old_data = {
            field.name: getattr(instance, field.name)
            for field in instance._meta.fields
        }

        # Log deletion
        self.audit_service.log_admin_action(
            user=self.request.user,
            action='delete',
            resource=instance,
            old_data=old_data,
            request=self.request
        )

        instance.delete()

    @action(detail=False, methods=['post'], permission_classes=[BulkOperationPermission])
    def bulk_update(self, request):
        """Bulk update operation"""
        data = request.data
        if not isinstance(data, list):
            return Response({
                'success': False,
                'error': 'Expected list of objects'
            }, status=status.HTTP_400_BAD_REQUEST)

        updated_objects = []
        errors = []

        with transaction.atomic():
            for i, item_data in enumerate(data):
                try:
                    if 'id' not in item_data:
                        errors.append(f'Item {i}: Missing id field')
                        continue

                    instance = self.get_queryset().get(id=item_data['id'])
                    serializer = self.get_serializer(instance, data=item_data, partial=True)

                    if serializer.is_valid():
                        self.perform_update(serializer)
                        updated_objects.append(serializer.data)
                    else:
                        errors.append(f'Item {i}: {serializer.errors}')

                except self.queryset.model.DoesNotExist:
                    errors.append(f'Item {i}: Object not found')
                except Exception as e:
                    errors.append(f'Item {i}: {str(e)}')

        return Response({
            'success': len(errors) == 0,
            'updated_count': len(updated_objects),
            'updated_objects': updated_objects,
            'errors': errors
        })

    @action(detail=False, methods=['post'])
    def bulk_delete(self, request):
        """Bulk delete operation"""
        ids = request.data.get('ids', [])
        if not isinstance(ids, list):
            return Response({
                'success': False,
                'error': 'Expected list of IDs'
            }, status=status.HTTP_400_BAD_REQUEST)

        deleted_count = 0
        errors = []

        with transaction.atomic():
            for obj_id in ids:
                try:
                    instance = self.get_queryset().get(id=obj_id)
                    self.perform_destroy(instance)
                    deleted_count += 1
                except self.queryset.model.DoesNotExist:
                    errors.append(f'Object with id {obj_id} not found')
                except Exception as e:
                    errors.append(f'Error deleting {obj_id}: {str(e)}')

        return Response({
            'success': len(errors) == 0,
            'deleted_count': deleted_count,
            'errors': errors
        })

    def list(self, request, *args, **kwargs):
        """Override list to add metadata"""
        response = super().list(request, *args, **kwargs)

        # Add user permissions to response metadata
        from .services import RoleService
        role_service = RoleService()

        if hasattr(response, 'data') and isinstance(response.data, dict):
            response.data['meta'] = {
                'user_permissions': role_service.get_user_permissions(request.user),
                'user_roles': role_service.get_user_roles(request.user),
                'user_context': role_service.get_user_context(request.user)
            }

        return response
```

#### **Step 4.2: Create Product Management APIs**

```python
# apps/admin_api/serializers.py
from rest_framework import serializers
from apps.products.models import Product, Category, Brand, ProductType, ProductVariant
from apps.products.serializers import ProductSerializer as BaseProductSerializer

class AdminProductSerializer(BaseProductSerializer):
    """Enhanced product serializer for admin use"""

    # Add admin-specific fields
    created_by = serializers.CharField(source='created_by.email', read_only=True)
    updated_by = serializers.CharField(source='updated_by.email', read_only=True)

    # Add variant count
    variant_count = serializers.SerializerMethodField()

    # Add stock summary
    total_stock = serializers.SerializerMethodField()

    class Meta(BaseProductSerializer.Meta):
        fields = BaseProductSerializer.Meta.fields + [
            'created_by', 'updated_by', 'variant_count', 'total_stock'
        ]

    def get_variant_count(self, obj):
        return obj.product_variant.count()

    def get_total_stock(self, obj):
        return sum(variant.stock_qty for variant in obj.product_variant.all())

class AdminCategorySerializer(serializers.ModelSerializer):
    """Enhanced category serializer for admin use"""

    product_count = serializers.SerializerMethodField()
    children_count = serializers.SerializerMethodField()
    full_path = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = '__all__'

    def get_product_count(self, obj):
        return obj.products.count()

    def get_children_count(self, obj):
        return obj.get_children().count()

    def get_full_path(self, obj):
        return str(obj)  # Uses the __str__ method which shows full path

# apps/admin_api/views.py (add to existing file)
from apps.products.models import Product, Category, Brand, ProductType
from .serializers import AdminProductSerializer, AdminCategorySerializer

class ProductAdminViewSet(BaseAdminViewSet):
    """Admin API for product management"""

    queryset = Product.objects.select_related('brand', 'category', 'product_type').prefetch_related('product_variant')
    serializer_class = AdminProductSerializer
    required_roles = ['ProductManager', 'ProductEditor', 'SuperAdmin']
    required_permissions = ['products.view']
    bulk_permissions = ['products.bulk_update']
    contextual_permissions = {'brand_id': 'brand_access'}

    filterset_fields = ['brand', 'category', 'product_type', 'is_active']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_permissions(self):
        """Dynamic permissions based on action"""
        if self.action == 'create':
            self.required_permissions = ['products.add']
        elif self.action in ['update', 'partial_update']:
            self.required_permissions = ['products.change']
        elif self.action == 'destroy':
            self.required_permissions = ['products.delete']
        elif self.action in ['bulk_update', 'bulk_delete']:
            self.required_permissions = ['products.bulk_update']

        return super().get_permissions()

    @action(detail=False, methods=['get'])
    def export(self, request):
        """Export products to CSV"""
        # Check export permission
        if not self.permission_checker.has_permission(request.user, 'products.export'):
            return Response({
                'success': False,
                'error': 'Insufficient permissions for export'
            }, status=status.HTTP_403_FORBIDDEN)

        # Implementation for CSV export
        import csv
        from django.http import HttpResponse

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="products.csv"'

        writer = csv.writer(response)
        writer.writerow(['ID', 'Title', 'Brand', 'Category', 'Price', 'Stock', 'Active'])

        for product in self.get_queryset():
            writer.writerow([
                product.id,
                product.title,
                product.brand.title,
                product.category.title,
                product.product_variant.first().price if product.product_variant.exists() else 0,
                sum(v.stock_qty for v in product.product_variant.all()),
                product.is_active
            ])

        # Log export action
        self.audit_service.log_admin_action(
            user=request.user,
            action='export',
            resource=None,
            request=request
        )

        return response

class CategoryAdminViewSet(BaseAdminViewSet):
    """Admin API for category management"""

    queryset = Category.objects.all()
    serializer_class = AdminCategorySerializer
    required_roles = ['ProductManager', 'CategoryManager', 'SuperAdmin']
    required_permissions = ['categories.view']

    def get_permissions(self):
        """Dynamic permissions based on action"""
        if self.action == 'create':
            self.required_permissions = ['categories.add']
        elif self.action in ['update', 'partial_update']:
            self.required_permissions = ['categories.change']
        elif self.action == 'destroy':
            self.required_permissions = ['categories.delete']
        elif self.action == 'reorder':
            self.required_permissions = ['categories.reorder']

        return super().get_permissions()

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """Reorder categories in tree structure"""
        data = request.data.get('categories', [])

        with transaction.atomic():
            for item in data:
                try:
                    category = Category.objects.get(id=item['id'])
                    if 'parent_id' in item:
                        parent = Category.objects.get(id=item['parent_id']) if item['parent_id'] else None
                        category.parent = parent
                    if 'order' in item:
                        category.order = item['order']
                    category.save()

                    # Log reorder action
                    self.audit_service.log_admin_action(
                        user=request.user,
                        action='reorder',
                        resource=category,
                        new_data=item,
                        request=request
                    )

                except Category.DoesNotExist:
                    continue

        return Response({'success': True, 'message': 'Categories reordered successfully'})
```

#### **Step 4.3: Create URL Configuration**

```python
# apps/admin_api/urls.py
from rest_framework.routers import DefaultRouter
from django.urls import path, include
from .views import ProductAdminViewSet, CategoryAdminViewSet
from .auth_views import AdminAuthViewSet

router = DefaultRouter()
router.register('products', ProductAdminViewSet, basename='admin-products')
router.register('categories', CategoryAdminViewSet, basename='admin-categories')
router.register('auth', AdminAuthViewSet, basename='admin-auth')

urlpatterns = [
    path('', include(router.urls)),
]

# pc_hardware/urls.py (update existing file)
urlpatterns = [
    # ... existing patterns
    path('api/admin/', include('apps.admin_api.urls')),
    # ... rest of patterns
]
```

#### **Step 4.4: Create Authentication Views**

```python
# apps/admin_api/auth_views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from .authentication import AdminAccessToken
from .services import TokenService, SecurityService, AuditService
from .permissions import SuperAdminRequired

class AdminAuthViewSet(viewsets.ViewSet):
    """Admin authentication endpoints"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.token_service = TokenService()
        self.security_service = SecurityService()
        self.audit_service = AuditService()

    @action(detail=False, methods=['post'])
    def login(self, request):
        """Admin login with enhanced security"""
        email = request.data.get('email')
        password = request.data.get('password')

        if not email or not password:
            return Response({
                'success': False,
                'error': 'Email and password required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user
        user = authenticate(request, username=email, password=password)

        if not user:
            self.audit_service.log_security_event(
                event_type='login_failed',
                user=None,
                ip_address=self.security_service.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'email': email}
            )
            return Response({
                'success': False,
                'error': 'Invalid credentials'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_staff:
            return Response({
                'success': False,
                'error': 'Admin access required'
            }, status=status.HTTP_403_FORBIDDEN)

        # Check IP whitelist
        if not self.security_service.is_ip_whitelisted(request):
            self.audit_service.log_security_event(
                event_type='ip_blocked_login',
                user=user,
                ip_address=self.security_service.get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                details={'email': email}
            )
            return Response({
                'success': False,
                'error': 'Access denied from this IP'
            }, status=status.HTTP_403_FORBIDDEN)

        # Generate tokens
        access_token = AdminAccessToken.for_user(user)
        refresh_token = RefreshToken.for_user(user)

        # Track session
        self.security_service.track_session(user, str(access_token['jti']))

        # Log successful login
        self.audit_service.log_security_event(
            event_type='login_success',
            user=user,
            ip_address=self.security_service.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', ''),
            details={'session_id': str(access_token['jti'])}
        )

        return Response({
            'success': True,
            'access_token': str(access_token),
            'refresh_token': str(refresh_token),
            'user': {
                'id': user.id,
                'email': user.email,
                'roles': access_token['roles'],
                'permissions': access_token['permissions']
            }
        })

    @action(detail=False, methods=['post'], permission_classes=[IsAuthenticated])
    def logout(self, request):
        """Admin logout with token revocation"""
        # Get token from request
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if auth_header.startswith('Bearer '):
            token_str = auth_header[7:]
            try:
                token = AdminAccessToken(token_str)
                jti = token['jti']

                # Revoke token
                self.token_service.revoke_token(jti, request.user, 'manual_logout')

                # Log logout
                self.audit_service.log_security_event(
                    event_type='logout',
                    user=request.user,
                    ip_address=self.security_service.get_client_ip(request),
                    user_agent=request.META.get('HTTP_USER_AGENT', ''),
                    details={'session_id': jti}
                )

            except Exception:
                pass  # Token might be invalid, but still return success

        return Response({'success': True, 'message': 'Logged out successfully'})

    @action(detail=False, methods=['post'], permission_classes=[SuperAdminRequired])
    def revoke_user_sessions(self, request):
        """Revoke all sessions for a user (SuperAdmin only)"""
        user_id = request.data.get('user_id')
        reason = request.data.get('reason', 'admin_revocation')

        if not user_id:
            return Response({
                'success': False,
                'error': 'user_id required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            from django.contrib.auth import get_user_model
            User = get_user_model()
            target_user = User.objects.get(id=user_id)

            # Revoke all tokens
            self.token_service.revoke_all_user_tokens(target_user, reason)

            # Log action
            self.audit_service.log_admin_action(
                user=request.user,
                action='revoke_sessions',
                resource=target_user,
                new_data={'reason': reason},
                request=request
            )

            return Response({
                'success': True,
                'message': f'All sessions revoked for user {target_user.email}'
            })

        except User.DoesNotExist:
            return Response({
                'success': False,
                'error': 'User not found'
            }, status=status.HTTP_404_NOT_FOUND)
```

---

### **Phase 5: Integration & Testing (Week 6)**

#### **Step 5.1: Update Existing Permission Classes**

```python
# utils/permissions.py (update existing file)
# Add import at the top
from apps.admin_api.services import RoleService, PermissionChecker

# Update existing classes to integrate with RBAC
class IsAdminOrReadOnly(permissions.BasePermission):
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True

        if not request.user.is_authenticated or not request.user.is_staff:
            return False

        # Use RBAC system for admin users
        if hasattr(request.user, 'admin_roles'):
            permission_checker = PermissionChecker()
            required_permission = f"{view.basename}.change" if hasattr(view, 'basename') else None
            if required_permission:
                return permission_checker.has_permission(request.user, required_permission)

        # Fallback to original logic
        return bool(request.user and request.user.is_staff)

# Keep other classes but add RBAC integration where needed
```

#### **Step 5.2: Create Admin Interface for Role Management**

```python
# apps/admin_api/admin.py
from django.contrib import admin
from .models import Role, Permission, UserRole, RolePermission, AuditLog, SecurityAuditLog

@admin.register(Role)
class RoleAdmin(admin.ModelAdmin):
    list_display = ['name', 'display_name', 'parent_role', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent_role']
    search_fields = ['name', 'display_name']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(Permission)
class PermissionAdmin(admin.ModelAdmin):
    list_display = ['codename', 'name', 'content_type', 'is_active']
    list_filter = ['content_type', 'is_active']
    search_fields = ['codename', 'name']

@admin.register(UserRole)
class UserRoleAdmin(admin.ModelAdmin):
    list_display = ['user', 'role', 'assigned_at', 'expires_at', 'is_active']
    list_filter = ['role', 'is_active', 'assigned_at']
    search_fields = ['user__email', 'role__name']
    readonly_fields = ['assigned_at']

@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = ['user', 'action', 'resource_type', 'resource_id', 'timestamp']
    list_filter = ['action', 'resource_type', 'timestamp']
    search_fields = ['user__email', 'action']
    readonly_fields = ['timestamp']

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

@admin.register(SecurityAuditLog)
class SecurityAuditLogAdmin(admin.ModelAdmin):
    list_display = ['event_type', 'user', 'ip_address', 'timestamp']
    list_filter = ['event_type', 'timestamp']
    search_fields = ['user__email', 'ip_address']
    readonly_fields = ['timestamp']

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False
```

#### **Step 5.3: Create Test Suite**

```python
# apps/admin_api/tests.py
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status
from .models import Role, Permission, UserRole
from .services import RoleService, PermissionChecker
from apps.products.models import Product, Brand, Category, ProductType

User = get_user_model()

class RBACTestCase(TestCase):
    def setUp(self):
        # Create test users
        self.superadmin = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        self.product_manager = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )

        # Create roles
        self.super_admin_role = Role.objects.create(
            name='SuperAdmin',
            display_name='Super Administrator'
        )

        self.product_manager_role = Role.objects.create(
            name='ProductManager',
            display_name='Product Manager'
        )

        # Create permissions
        self.product_view_perm = Permission.objects.create(
            codename='products.view',
            name='Can view products'
        )

        self.product_change_perm = Permission.objects.create(
            codename='products.change',
            name='Can change products'
        )

        # Assign roles
        UserRole.objects.create(user=self.superadmin, role=self.super_admin_role)
        UserRole.objects.create(user=self.product_manager, role=self.product_manager_role)

        self.client = APIClient()

    def test_role_service_get_user_roles(self):
        """Test role service returns correct roles"""
        role_service = RoleService()

        superadmin_roles = role_service.get_user_roles(self.superadmin)
        self.assertIn('SuperAdmin', superadmin_roles)

        pm_roles = role_service.get_user_roles(self.product_manager)
        self.assertIn('ProductManager', pm_roles)

    def test_permission_checker(self):
        """Test permission checking logic"""
        permission_checker = PermissionChecker()

        # SuperAdmin should have all permissions
        self.assertTrue(
            permission_checker.has_permission(self.superadmin, 'products.view')
        )

        # Product manager should only have assigned permissions
        # (This would need proper permission assignment in setup)

    def test_admin_api_authentication(self):
        """Test admin API authentication"""
        # Test login
        response = self.client.post('/api/admin/auth/login/', {
            'email': '<EMAIL>',
            'password': 'testpass123'
        })

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('access_token', response.data)

    def test_contextual_permissions(self):
        """Test contextual permission filtering"""
        # Create test data with different brands
        brand1 = Brand.objects.create(title='Brand 1', slug='brand-1')
        brand2 = Brand.objects.create(title='Brand 2', slug='brand-2')

        # Assign brand-specific context to user
        user_role = UserRole.objects.get(user=self.product_manager)
        user_role.context_data = {'brand_access': [brand1.id]}
        user_role.save()

        # Test that user can only access products from assigned brands
        # (Implementation would depend on actual product creation)

class SecurityTestCase(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            is_staff=True
        )
        self.client = APIClient()

    def test_ip_whitelist(self):
        """Test IP whitelist functionality"""
        from .services import SecurityService
        from django.test import RequestFactory

        security_service = SecurityService()
        factory = RequestFactory()

        # Test with allowed IP
        request = factory.get('/')
        request.META['REMOTE_ADDR'] = '127.0.0.1'
        self.assertTrue(security_service.is_ip_whitelisted(request))

    def test_token_revocation(self):
        """Test token revocation"""
        from .services import TokenService
        import uuid

        token_service = TokenService()
        jti = str(uuid.uuid4())

        # Revoke token
        token_service.revoke_token(jti, self.user)

        # Check if token is revoked
        from .authentication import AdminJWTAuthentication
        auth = AdminJWTAuthentication()
        self.assertTrue(auth.is_token_revoked(jti))
```

---

### **Phase 6: Deployment & Monitoring (Week 7-8)**

#### **Step 6.1: Production Settings**

```python
# pc_hardware/settings/production.py (create new file)
from .common import *

# Security settings for production
DEBUG = False
ALLOWED_HOSTS = ['your-domain.com', 'api.your-domain.com']

# Enhanced security for admin
ADMIN_IP_WHITELIST = [
    '10.0.0.0/8',      # Internal network
    '***********/24',  # Office network
    # Add specific admin IPs
]

# Stricter JWT settings
SIMPLE_JWT.update({
    'ACCESS_TOKEN_LIFETIME': timedelta(minutes=15),
    'REFRESH_TOKEN_LIFETIME': timedelta(hours=24),
    'ADMIN_ACCESS_TOKEN_LIFETIME': timedelta(hours=4),  # Shorter in production
})

# Redis settings for production
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis-server:6379/1',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {'max_connections': 50},
        }
    },
    'tokens': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': 'redis://redis-server:6379/2',
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
            'CONNECTION_POOL_KWARGS': {'max_connections': 50},
        }
    }
}

# Database optimization
DATABASES['default'].update({
    'CONN_MAX_AGE': 60,
    'OPTIONS': {
        'MAX_CONNS': 20,
        'MIN_CONNS': 5,
    }
})

# Logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/admin_audit.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'security_file': {
            'level': 'WARNING',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': '/var/log/django/security_audit.log',
            'maxBytes': 1024*1024*15,  # 15MB
            'backupCount': 10,
            'formatter': 'verbose',
        },
        'console': {
            'level': 'ERROR',
            'class': 'logging.StreamHandler',
            'formatter': 'simple'
        },
    },
    'loggers': {
        'apps.admin_api': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'security': {
            'handlers': ['security_file', 'console'],
            'level': 'WARNING',
            'propagate': True,
        },
        'django.security': {
            'handlers': ['security_file'],
            'level': 'WARNING',
            'propagate': True,
        },
    },
}
```

#### **Step 6.2: Create Monitoring & Health Checks**

```python
# apps/admin_api/monitoring.py
from django.http import JsonResponse
from django.core.cache import caches
from django.db import connection
from django.conf import settings
import redis
import time
from .models import AuditLog, SecurityAuditLog

class HealthCheckService:
    """Service for system health monitoring"""

    def get_system_health(self):
        """Comprehensive system health check"""
        health_data = {
            'status': 'healthy',
            'timestamp': time.time(),
            'checks': {}
        }

        # Database check
        health_data['checks']['database'] = self._check_database()

        # Redis check
        health_data['checks']['redis'] = self._check_redis()

        # Cache check
        health_data['checks']['cache'] = self._check_cache()

        # RBAC system check
        health_data['checks']['rbac'] = self._check_rbac_system()

        # Determine overall status
        if any(check['status'] == 'unhealthy' for check in health_data['checks'].values()):
            health_data['status'] = 'unhealthy'
        elif any(check['status'] == 'degraded' for check in health_data['checks'].values()):
            health_data['status'] = 'degraded'

        return health_data

    def _check_database(self):
        """Check database connectivity"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                return {'status': 'healthy', 'response_time': 0.001}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

    def _check_redis(self):
        """Check Redis connectivity"""
        try:
            cache = caches['tokens']
            cache.set('health_check', 'ok', 10)
            result = cache.get('health_check')
            if result == 'ok':
                return {'status': 'healthy'}
            else:
                return {'status': 'degraded', 'error': 'Cache not working properly'}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

    def _check_cache(self):
        """Check cache performance"""
        try:
            cache = caches['default']
            start_time = time.time()
            cache.set('perf_test', 'data', 10)
            cache.get('perf_test')
            response_time = time.time() - start_time

            if response_time < 0.01:  # 10ms
                return {'status': 'healthy', 'response_time': response_time}
            elif response_time < 0.1:  # 100ms
                return {'status': 'degraded', 'response_time': response_time}
            else:
                return {'status': 'unhealthy', 'response_time': response_time}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

    def _check_rbac_system(self):
        """Check RBAC system functionality"""
        try:
            from .models import Role, Permission

            # Check if core roles exist
            core_roles = ['SuperAdmin', 'ProductManager', 'OrderManager']
            existing_roles = Role.objects.filter(name__in=core_roles, is_active=True).count()

            if existing_roles == len(core_roles):
                return {'status': 'healthy', 'roles_count': existing_roles}
            else:
                return {'status': 'degraded', 'roles_count': existing_roles, 'expected': len(core_roles)}
        except Exception as e:
            return {'status': 'unhealthy', 'error': str(e)}

# apps/admin_api/views.py (add to existing file)
class SystemAdminViewSet(viewsets.ViewSet):
    """System administration endpoints"""

    permission_classes = [SuperAdminRequired]

    @action(detail=False, methods=['get'])
    def health(self, request):
        """System health check"""
        health_service = HealthCheckService()
        health_data = health_service.get_system_health()

        status_code = 200
        if health_data['status'] == 'unhealthy':
            status_code = 503
        elif health_data['status'] == 'degraded':
            status_code = 200  # Still operational

        return Response(health_data, status=status_code)

    @action(detail=False, methods=['get'])
    def metrics(self, request):
        """System metrics"""
        from django.db import connection

        # Get database metrics
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    schemaname,
                    tablename,
                    n_tup_ins as inserts,
                    n_tup_upd as updates,
                    n_tup_del as deletes
                FROM pg_stat_user_tables
                WHERE schemaname = 'public'
                ORDER BY n_tup_ins + n_tup_upd + n_tup_del DESC
                LIMIT 10
            """)
            db_stats = cursor.fetchall()

        # Get audit log metrics
        recent_actions = AuditLog.objects.filter(
            timestamp__gte=timezone.now() - timedelta(hours=24)
        ).values('action').annotate(count=Count('action')).order_by('-count')[:10]

        security_events = SecurityAuditLog.objects.filter(
            timestamp__gte=timezone.now() - timedelta(hours=24)
        ).values('event_type').annotate(count=Count('event_type')).order_by('-count')[:10]

        return Response({
            'database_stats': db_stats,
            'recent_actions': list(recent_actions),
            'security_events': list(security_events),
            'timestamp': timezone.now()
        })

    @action(detail=False, methods=['post'])
    def clear_cache(self, request):
        """Clear system cache"""
        cache_type = request.data.get('cache_type', 'all')

        try:
            if cache_type == 'all' or cache_type == 'default':
                caches['default'].clear()

            if cache_type == 'all' or cache_type == 'tokens':
                caches['tokens'].clear()

            # Log cache clear action
            self.audit_service.log_admin_action(
                user=request.user,
                action='clear_cache',
                resource=None,
                new_data={'cache_type': cache_type},
                request=request
            )

            return Response({
                'success': True,
                'message': f'Cache {cache_type} cleared successfully'
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
```

#### **Step 6.3: Create Deployment Scripts**

```bash
#!/bin/bash
# deploy_rbac.sh - Deployment script for RBAC system

set -e

echo "Starting RBAC deployment..."

# Backup database
echo "Creating database backup..."
python manage.py dbbackup

# Run migrations
echo "Running migrations..."
python manage.py migrate

# Setup RBAC system
echo "Setting up RBAC system..."
python manage.py setup_rbac

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Clear cache
echo "Clearing cache..."
python manage.py shell -c "
from django.core.cache import caches
caches['default'].clear()
caches['tokens'].clear()
print('Cache cleared')
"

# Restart services
echo "Restarting services..."
sudo systemctl restart gunicorn
sudo systemctl restart nginx
sudo systemctl restart redis
sudo systemctl restart celery

echo "RBAC deployment completed successfully!"
```

#### **Step 6.4: Create Monitoring Dashboard**

```python
# apps/admin_api/dashboard.py
from django.shortcuts import render
from django.contrib.admin.views.decorators import staff_member_required
from django.utils import timezone
from datetime import timedelta
from .models import AuditLog, SecurityAuditLog, UserRole
from django.db.models import Count

@staff_member_required
def rbac_dashboard(request):
    """RBAC monitoring dashboard"""

    # Get recent activity
    recent_actions = AuditLog.objects.filter(
        timestamp__gte=timezone.now() - timedelta(days=7)
    ).values('action').annotate(count=Count('action')).order_by('-count')[:10]

    # Get security events
    security_events = SecurityAuditLog.objects.filter(
        timestamp__gte=timezone.now() - timedelta(days=7)
    ).values('event_type').annotate(count=Count('event_type')).order_by('-count')[:10]

    # Get active admin users
    active_admins = UserRole.objects.filter(
        is_active=True,
        user__is_staff=True
    ).select_related('user', 'role').count()

    # Get role distribution
    role_distribution = UserRole.objects.filter(
        is_active=True
    ).values('role__name').annotate(count=Count('role')).order_by('-count')

    context = {
        'recent_actions': recent_actions,
        'security_events': security_events,
        'active_admins': active_admins,
        'role_distribution': role_distribution,
    }

    return render(request, 'admin/rbac_dashboard.html', context)
```

---

### **Phase 7: Documentation & Training (Week 8)**

#### **Step 7.1: API Documentation**

```python
# apps/admin_api/schema.py
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes

# Add to views for automatic documentation
@extend_schema(
    summary="Admin Product Management",
    description="Comprehensive product management API for admin users with role-based access control",
    parameters=[
        OpenApiParameter(
            name='brand',
            type=OpenApiTypes.INT,
            location=OpenApiParameter.QUERY,
            description='Filter by brand ID'
        ),
    ],
    tags=['Admin - Products']
)
class ProductAdminViewSet(BaseAdminViewSet):
    # ... existing code
```

#### **Step 7.2: User Guide Creation**

```markdown
# RBAC System User Guide

## Overview
The Role-Based Access Control (RBAC) system provides secure, granular access control for admin operations.

## Role Hierarchy
- **SuperAdmin**: Full system access
- **ProductManager**: Product catalog management
- **OrderManager**: Order operations
- **CustomerManager**: Customer relations

## Getting Started
1. Login with admin credentials
2. Check your assigned roles and permissions
3. Access only the features you have permissions for

## API Usage Examples
```bash
# Login
curl -X POST /api/admin/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password"}'

# Use token for authenticated requests
curl -X GET /api/admin/products/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Security Best Practices

- Use strong passwords
- Access admin panel only from whitelisted IPs
- Log out when finished
- Report suspicious activity

```

---

## **Implementation Checklist**

### **Week 1: Foundation**
- [ ] Create admin_api app
- [ ] Update settings configuration
- [ ] Create RBAC models
- [ ] Implement enhanced JWT authentication
- [ ] Create core services
- [ ] Run initial migrations

### **Week 2: Permission System**
- [ ] Create enhanced permission classes
- [ ] Implement permission checker service
- [ ] Create security middleware
- [ ] Update logging configuration
- [ ] Test permission checking logic

### **Week 3: Role Setup**
- [ ] Create management commands
- [ ] Setup role hierarchy
- [ ] Create permission assignments
- [ ] Test role inheritance
- [ ] Assign initial admin roles

### **Week 4-5: API Implementation**
- [ ] Create base admin viewsets
- [ ] Implement product management APIs
- [ ] Create category management APIs
- [ ] Add bulk operations
- [ ] Implement contextual filtering

### **Week 6: Integration & Testing**
- [ ] Update existing permission classes
- [ ] Create admin interface
- [ ] Write comprehensive tests
- [ ] Test security features
- [ ] Performance testing

### **Week 7-8: Deployment & Monitoring**
- [ ] Configure production settings
- [ ] Create health checks
- [ ] Setup monitoring dashboard
- [ ] Deploy to production
- [ ] Create documentation

---

## **Success Metrics**

1. **Security**: Zero privilege escalation vulnerabilities
2. **Performance**: <50ms permission check latency
3. **Auditability**: 100% action coverage in audit logs
4. **Flexibility**: Role changes without code deployment
5. **Scalability**: Support for 1000+ concurrent admin users
6. **Compliance**: Meet SOC2 and GDPR requirements

---

## **Maintenance & Support**

### **Regular Tasks**
- Monitor audit logs for suspicious activity
- Review and update role permissions quarterly
- Clean up expired tokens and sessions
- Update IP whitelist as needed

### **Troubleshooting**
- Check health endpoint for system status
- Review security audit logs for access issues
- Clear cache if permission changes not reflecting
- Restart services if authentication fails

This comprehensive implementation guide provides a complete roadmap for implementing a production-ready RBAC system that integrates seamlessly with your existing Django application while maintaining security, performance, and scalability.
```

```
