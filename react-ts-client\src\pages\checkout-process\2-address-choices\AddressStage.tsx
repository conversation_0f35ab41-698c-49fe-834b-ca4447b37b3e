import { Link } from "react-router-dom"
import { useEffect, useState } from "react"
import { useNavigate } from 'react-router-dom'
import useCart from "../../../react-query/hooks/cart-hooks/useCart"
import authStore from "../../../zustand/authStore"
import cartStore from "../../../zustand/cartStore"
import Logo from "../../../components/utils/logo/Logo"
import Spinner from "../../../components/utils/spinner/Spinner"
import Alert from "../../../components/utils/alert/Alert"
import EmptyCart from '../../../components/utils/empty-cart/EmptyCart'
import useCustomerDetails from '../../../react-query/hooks/customer-related-hooks/useCustomerDetails'
import { AddressFormInputs } from '../../auth/profile/addresses/ManageAddresses'
import CartItemsList from '../../../components/checkout/cart/CartItemsList'
import PriceSummary from '../../../components/checkout/price-summary/PriceSummary'
import useDeleteCartItem from '../../../react-query/hooks/cart-hooks/useDeleteCartItem'
import styles from './AddressStage.module.scss'

const AddressStage = () => {
  const navigate = useNavigate()

  const { isLoggedIn } = authStore()
  const { data: customer } = useCustomerDetails(isLoggedIn)
  const { cartId, setSelectedAddress, selectedAddress } = cartStore()
  const { isPending, error, data, } = useCart()
  const { mutate: deleteCartItem } = useDeleteCartItem()

  const [addressesReady, setAddressesReady] = useState(false)

  useEffect(() => {
    if (!isLoggedIn) {
      navigate('/user/login')
    }
  }, [isLoggedIn, navigate])

  useEffect(() => {
    if (customer?.address && customer.address.length > 0) {
      if (!selectedAddress || Object.keys(selectedAddress).length === 0) {
        setSelectedAddress(customer.address[0])
      }
      setAddressesReady(true)
    }
  }, [customer, selectedAddress, setSelectedAddress])

  // Handle out-of-stock items by removing them
  useEffect(() => {
    if (data && data?.cart_items?.length > 0) {
      const outOfStockItems = data.cart_items.filter(item => item.product_variant.stock_qty === 0)

      if (outOfStockItems.length > 0) {
        outOfStockItems.forEach(item => {
          deleteCartItem(item.id) // Remove each out-of-stock item from the cart
        })
      }
    }
  }, [data, deleteCartItem])

  const handleAddressChange = (address: AddressFormInputs) => {
    setSelectedAddress(address)
  }

  return (
    <>
      {!cartId ? (
        <EmptyCart message='Your cart is empty. Add some products to the cart to checkout!' />
      ) : (
        <>
          {isPending ? (
            <Spinner color='#0091CF' size={20} loading />
          ) : (
            <>
              {error ? (
                <Alert variant="error" message={error.message} />
              ) : (
                <>
                  {(customer?.address?.length === 0 || customer?.phone_number === '') ? (
                    <>
                      <div className='logo_header'>
                        <Logo />
                      </div>
                      <div className={styles.missing_addresses}>
                        <Alert variant="warning" textSize='20' message="
                        You haven't added a shipping address yet. 
                        Please add one along with a phone number to continue with checkout. Thank you! 😊" />
                        <section className='btn_container'>
                          <button className='empty_btn' onClick={() => navigate('/customer')}>Update Profile</button>
                        </section>
                      </div>
                    </>
                  ) : (
                    <>
                      {!data || data.cart_items.length === 0 ? (
                        <div className={styles.empty_cart}>
                          <p>Your cart is empty. Add some products to the cart to checkout!</p>
                          <Link to='/'>Go Shopping </Link>
                        </div>
                      ) : (
                        <>
                          <section>
                            <div className='logo_header'>
                              <Logo />
                            </div>
                            <section className={`container ${styles.address_stage}`}>
                              <h3>Address Choices</h3>
                              <div className={styles.contact_details}>
                                <h3>Contact Details: </h3>
                                <p>Deliver to: {customer?.first_name} {customer?.last_name}</p>
                                <p>Phone: {customer?.phone_number}</p>
                                <p>Email to: {customer?.email}</p>
                              </div>
                              <hr />
                              <div className={styles.cart}>
                                <CartItemsList cartItems={data.cart_items} />
                                <PriceSummary
                                  totalPrice={data?.total_price}
                                  shippingCost={data?.shipping_cost}
                                  grandTotal={data?.grand_total}
                                  cart_weight={data?.cart_weight}
                                />
                              </div>
                              <hr />
                              {/* Render the addresses only when addresses are ready */}
                              {addressesReady && (
                                <div className={styles.address_selection}>
                                  <h3>Choose a shipping address: </h3>
                                  {customer?.address?.map((address) => (
                                    <address key={address.id}>
                                      <input
                                        type="radio"
                                        id={`address-${address.id}`}
                                        name="address"
                                        value={address.id}
                                        checked={selectedAddress?.id === address.id}
                                        onChange={() => handleAddressChange(address)}
                                      />
                                      <label htmlFor={`address-${address.id}`}>
                                        {address.full_name}, {address.street_name}, {address.city_or_village}
                                      </label>
                                    </address>
                                  ))}
                                  <button onClick={() => navigate('/checkout/place-order/')}>Use this address</button>
                                </div>
                              )}
                              <hr />
                            </section>
                          </section>
                        </>
                      )}
                    </>
                  )}
                </>
              )}
            </>
          )}
        </>
      )}
    </>
  )
}

export default AddressStage
